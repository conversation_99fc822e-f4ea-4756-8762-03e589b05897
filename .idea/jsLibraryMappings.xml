<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JavaScriptLibraryMappings">
    <excludedPredefinedLibrary name="flexit-fitness-monorepo/apps/mobile/android/app/.cxx/Debug/4y70m3kh/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/projects/flexit-fitness-monorepo/node_modules" />
    <excludedPredefinedLibrary name="flexit-fitness-monorepo/apps/mobile/android/app/.cxx/Debug/4y70m3kh/arm64-v8a/rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/projects/flexit-fitness-monorepo/node_modules" />
    <excludedPredefinedLibrary name="flexit-fitness-monorepo/apps/mobile/android/app/.cxx/Debug/4y70m3kh/arm64-v8a/rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/projects/flexit-fitness-monorepo/node_modules" />
    <excludedPredefinedLibrary name="flexit-fitness-monorepo/apps/mobile/android/app/.cxx/Debug/4y70m3kh/arm64-v8a/safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/projects/flexit-fitness-monorepo/node_modules" />
  </component>
</project>