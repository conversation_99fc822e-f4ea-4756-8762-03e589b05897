:root {
  font-family: <PERSON><PERSON>s, sans-serif;
  --font-family:<PERSON>pins, sans-serif;
  --surface-a:#161d21;
  --surface-b:#0e1315;
  --surface-c:rgba(158, 173, 230, 0.08);
  --surface-d:#263238;
  --surface-e:#161d21;
  --surface-f:#161d21;
  --text-color:rgba(255, 255, 255, 0.87);
  --text-color-secondary:rgba(255, 255, 255, 0.6);
  --primary-color:#9eade6;
  --primary-color-text:#121212;
  --surface-0: #0e1315;
  --surface-50: #262b2c;
  --surface-100: #3e4244;
  --surface-200: #565a5b;
  --surface-300: #6e7173;
  --surface-400: #87898a;
  --surface-500: #9fa1a1;
  --surface-600: #b7b8b9;
  --surface-700: #cfd0d0;
  --surface-800: #e7e7e8;
  --surface-900: #ffffff;
  --gray-50: #e7e7e8;
  --gray-100: #cfd0d0;
  --gray-200: #b7b8b9;
  --gray-300: #9fa1a1;
  --gray-400: #87898a;
  --gray-500: #6e7173;
  --gray-600: #565a5b;
  --gray-700: #3e4244;
  --gray-800: #262b2c;
  --gray-900: #0e1315;
  --content-padding:1rem;
  --inline-spacing:0.5rem;
  --border-radius:6px;
  --surface-ground:#0e1315;
  --surface-section:#0e1315;
  --surface-card:#161d21;
  --surface-overlay:#161d21;
  --surface-border:#263238;
  --surface-hover:rgba($primaryColor, .08);
  --focus-ring: 0 0 0 1px #9eade6;
  --maskbg: rgba(0, 0, 0, 0.4);
  --highlight-bg: rgba(158, 173, 230, 0.16);
  --highlight-text-color: #9eade6;
  color-scheme: dark;
}

/* poppins-300 - latin-ext_latin */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 300;
  src: local(""), url("./fonts/poppins-v15-latin-ext_latin-300.woff2") format("woff2"), url("./fonts/poppins-v15-latin-ext_latin-300.woff") format("woff");
  /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* poppins-regular - latin-ext_latin */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  src: local(""), url("./fonts/poppins-v15-latin-ext_latin-regular.woff2") format("woff2"), url("./fonts/poppins-v15-latin-ext_latin-regular.woff") format("woff");
  /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* poppins-600 - latin-ext_latin */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 600;
  src: local(""), url("./fonts/poppins-v15-latin-ext_latin-600.woff2") format("woff2"), url("./fonts/poppins-v15-latin-ext_latin-600.woff") format("woff");
  /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* poppins-700 - latin-ext_latin */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 700;
  src: local(""), url("./fonts/poppins-v15-latin-ext_latin-700.woff2") format("woff2"), url("./fonts/poppins-v15-latin-ext_latin-700.woff") format("woff");
  /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
:root {
  --blue-50:#f6fbfd;
  --blue-100:#d6eaf5;
  --blue-200:#b5d9ed;
  --blue-300:#95c8e4;
  --blue-400:#74b7dc;
  --blue-500:#54a6d4;
  --blue-600:#478db4;
  --blue-700:#3b7494;
  --blue-800:#2e5b75;
  --blue-900:#224255;
  --green-50:#f9fbf4;
  --green-100:#e3eccc;
  --green-200:#cddca4;
  --green-300:#b7cd7c;
  --green-400:#a1bd54;
  --green-500:#8bae2c;
  --green-600:#769425;
  --green-700:#617a1f;
  --green-800:#4c6018;
  --green-900:#384612;
  --yellow-50:#fffdf4;
  --yellow-100:#fff4cc;
  --yellow-200:#ffeba3;
  --yellow-300:#ffe27b;
  --yellow-400:#ffd952;
  --yellow-500:#ffd02a;
  --yellow-600:#d9b124;
  --yellow-700:#b3921d;
  --yellow-800:#8c7217;
  --yellow-900:#665311;
  --cyan-50:#f5fafc;
  --cyan-100:#cfe9f3;
  --cyan-200:#a8d8e9;
  --cyan-300:#82c7df;
  --cyan-400:#5bb5d6;
  --cyan-500:#35a4cc;
  --cyan-600:#2d8bad;
  --cyan-700:#25738f;
  --cyan-800:#1d5a70;
  --cyan-900:#154252;
  --pink-50:#fdf6f8;
  --pink-100:#f5d6dd;
  --pink-200:#edb5c2;
  --pink-300:#e495a8;
  --pink-400:#dc748d;
  --pink-500:#d45472;
  --pink-600:#b44761;
  --pink-700:#943b50;
  --pink-800:#752e3f;
  --pink-900:#55222e;
  --indigo-50:#f6f8fd;
  --indigo-100:#d6ddf5;
  --indigo-200:#b5c2ed;
  --indigo-300:#95a8e4;
  --indigo-400:#748ddc;
  --indigo-500:#5472d4;
  --indigo-600:#4761b4;
  --indigo-700:#3b5094;
  --indigo-800:#2e3f75;
  --indigo-900:#222e55;
  --teal-50:#f5fcfa;
  --teal-100:#d1f0e8;
  --teal-200:#ace3d6;
  --teal-300:#88d7c4;
  --teal-400:#63cbb2;
  --teal-500:#3fbfa0;
  --teal-600:#36a288;
  --teal-700:#2c8670;
  --teal-800:#236958;
  --teal-900:#194c40;
  --orange-50:#fffaf4;
  --orange-100:#ffe5cc;
  --orange-200:#ffd0a3;
  --orange-300:#ffbb7b;
  --orange-400:#ffa752;
  --orange-500:#ff922a;
  --orange-600:#d97c24;
  --orange-700:#b3661d;
  --orange-800:#8c5017;
  --orange-900:#663a11;
  --bluegray-50:#f9fafb;
  --bluegray-100:#e1e5ea;
  --bluegray-200:#c9d0d9;
  --bluegray-300:#b1bbc8;
  --bluegray-400:#99a6b7;
  --bluegray-500:#8191a6;
  --bluegray-600:#6e7b8d;
  --bluegray-700:#5a6674;
  --bluegray-800:#47505b;
  --bluegray-900:#343a42;
  --purple-50:#f8f6fd;
  --purple-100:#ded6f5;
  --purple-200:#c4b5ed;
  --purple-300:#aa95e4;
  --purple-400:#9074dc;
  --purple-500:#7654d4;
  --purple-600:#6447b4;
  --purple-700:#533b94;
  --purple-800:#412e75;
  --purple-900:#2f2255;
  --red-50:#fff7f6;
  --red-100:#ffd8d2;
  --red-200:#ffb9af;
  --red-300:#ff9b8b;
  --red-400:#ff7c68;
  --red-500:#ff5d44;
  --red-600:#d94f3a;
  --red-700:#b34130;
  --red-800:#8c3325;
  --red-900:#66251b;
  --primary-50:#fafbfe;
  --primary-100:#e8ebf9;
  --primary-200:#d5dcf4;
  --primary-300:#c3ccf0;
  --primary-400:#b0bdeb;
  --primary-500:#9eade6;
  --primary-600:#8693c4;
  --primary-700:#6f79a1;
  --primary-800:#575f7f;
  --primary-900:#3f455c;
}

.p-editor-container .p-editor-toolbar {
  background: #161d21;
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
}
.p-editor-container .p-editor-toolbar.ql-snow {
  border: 2px solid #263238;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-stroke {
  stroke: rgba(255, 255, 255, 0.6);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-fill {
  fill: rgba(255, 255, 255, 0.6);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label {
  border: 0 none;
  color: rgba(255, 255, 255, 0.6);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover {
  color: rgba(255, 255, 255, 0.87);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-stroke {
  stroke: rgba(255, 255, 255, 0.87);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-fill {
  fill: rgba(255, 255, 255, 0.87);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: rgba(255, 255, 255, 0.87);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: rgba(255, 255, 255, 0.87);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: rgba(255, 255, 255, 0.87);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  background: #161d21;
  border: 1px solid #263238;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 6px;
  padding: 0.5rem 0.5rem;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item {
  color: rgba(255, 255, 255, 0.87);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item:hover {
  color: rgba(255, 255, 255, 0.87);
  background: rgba(158, 173, 230, 0.08);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded:not(.ql-icon-picker) .ql-picker-item {
  padding: 0.5rem 1rem;
}
.p-editor-container .p-editor-content {
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}
.p-editor-container .p-editor-content.ql-snow {
  border: 2px solid #263238;
}
.p-editor-container .p-editor-content .ql-editor {
  background: #0e1315;
  color: rgba(255, 255, 255, 0.87);
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}
.p-editor-container .ql-snow.ql-toolbar button:hover,
.p-editor-container .ql-snow.ql-toolbar button:focus {
  color: rgba(255, 255, 255, 0.87);
}
.p-editor-container .ql-snow.ql-toolbar button:hover .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar button:focus .ql-stroke {
  stroke: rgba(255, 255, 255, 0.87);
}
.p-editor-container .ql-snow.ql-toolbar button:hover .ql-fill,
.p-editor-container .ql-snow.ql-toolbar button:focus .ql-fill {
  fill: rgba(255, 255, 255, 0.87);
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected {
  color: #9eade6;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke {
  stroke: #9eade6;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-fill,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill {
  fill: #9eade6;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-picker-label,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-picker-label,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-picker-label {
  color: #9eade6;
}

@layer primereact {
  * {
    box-sizing: border-box;
  }

  .p-component {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    font-weight: normal;
  }

  .p-component-overlay {
    background-color: rgba(0, 0, 0, 0.4);
    transition-duration: 0.3s;
  }

  .p-disabled, .p-component:disabled {
    opacity: 0.4;
  }

  .p-error {
    color: #f78c79;
  }

  .p-text-secondary {
    color: rgba(255, 255, 255, 0.6);
  }

  .pi {
    font-size: 1rem;
  }

  .p-icon {
    width: 1rem;
    height: 1rem;
  }

  .p-link {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    border-radius: 6px;
  }
  .p-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }

  .p-component-overlay-enter {
    animation: p-component-overlay-enter-animation 150ms forwards;
  }

  .p-component-overlay-leave {
    animation: p-component-overlay-leave-animation 150ms forwards;
  }

  @keyframes p-component-overlay-enter-animation {
    from {
      background-color: transparent;
    }
    to {
      background-color: var(--maskbg);
    }
  }
  @keyframes p-component-overlay-leave-animation {
    from {
      background-color: var(--maskbg);
    }
    to {
      background-color: transparent;
    }
  }

  .p-autocomplete .p-autocomplete-loader {
    right: 0.75rem;
  }
  .p-autocomplete.p-autocomplete-dd .p-autocomplete-loader {
    right: 3.607rem;
  }
  .p-autocomplete .p-autocomplete-multiple-container {
    padding: 0.25rem 0.75rem;
    gap: 0.5rem;
  }
  .p-autocomplete .p-autocomplete-multiple-container:not(.p-disabled):hover {
    border-color: #2d3e44;
  }
  .p-autocomplete .p-autocomplete-multiple-container:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token {
    padding: 0.25rem 0;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token input {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.87);
    padding: 0;
    margin: 0;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token {
    padding: 0.25rem 0.75rem;
    margin-right: 0.5rem;
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
    border-radius: 6px;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token .p-autocomplete-token-icon {
    margin-left: 0.5rem;
  }
  .p-autocomplete.p-invalid.p-component > .p-inputtext {
    border-color: #f78c79;
  }

  .p-autocomplete-panel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 1px solid #263238;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-autocomplete-panel .p-autocomplete-items {
    padding: 0.5rem 0.5rem;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
    margin: 0 0 4px 0;
    padding: 0.5rem 1rem;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item:hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item-group {
    margin: 0;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
  }

  .p-calendar.p-invalid.p-component > .p-inputtext {
    border-color: #f78c79;
  }

  .p-datepicker {
    padding: 0.5rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
  }
  .p-datepicker:not(.p-datepicker-inline) {
    background: #161d21;
    border: 1px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-datepicker:not(.p-datepicker-inline) .p-datepicker-header {
    background: #161d21;
  }
  .p-datepicker .p-datepicker-header {
    padding: 0.5rem;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
    margin: 0;
    border-bottom: 1px solid #263238;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev,
.p-datepicker .p-datepicker-header .p-datepicker-next {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev:enabled:hover,
.p-datepicker .p-datepicker-header .p-datepicker-next:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev:focus-visible,
.p-datepicker .p-datepicker-header .p-datepicker-next:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title {
    line-height: 2rem;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year,
.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
    color: rgba(255, 255, 255, 0.87);
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    font-weight: 600;
    padding: 0.5rem;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year:enabled:hover,
.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month:enabled:hover {
    color: #9eade6;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
    margin-right: 0.5rem;
  }
  .p-datepicker table {
    font-size: 1rem;
    margin: 0.5rem 0;
  }
  .p-datepicker table th {
    padding: 0.5rem;
  }
  .p-datepicker table th > span {
    width: 2.5rem;
    height: 2.5rem;
  }
  .p-datepicker table td {
    padding: 0.5rem;
  }
  .p-datepicker table td > span {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    transition: box-shadow 0.3s;
    border: 1px solid transparent;
  }
  .p-datepicker table td > span.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-datepicker table td > span:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-datepicker table td.p-datepicker-today > span {
    background: transparent;
    color: #9eade6;
    border-color: transparent;
  }
  .p-datepicker table td.p-datepicker-today > span.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-datepicker .p-datepicker-buttonbar {
    padding: 1rem 0;
    border-top: 1px solid #263238;
  }
  .p-datepicker .p-datepicker-buttonbar .p-button {
    width: auto;
  }
  .p-datepicker .p-timepicker {
    border-top: 1px solid #263238;
    padding: 0.5rem;
  }
  .p-datepicker .p-timepicker button {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-datepicker .p-timepicker button:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-datepicker .p-timepicker button:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-datepicker .p-timepicker button:last-child {
    margin-top: 0.2em;
  }
  .p-datepicker .p-timepicker span {
    font-size: 1.25rem;
  }
  .p-datepicker .p-timepicker > div {
    padding: 0 0.5rem;
  }
  .p-datepicker.p-datepicker-timeonly .p-timepicker {
    border-top: 0 none;
  }
  .p-datepicker .p-monthpicker {
    margin: 0.5rem 0;
  }
  .p-datepicker .p-monthpicker .p-monthpicker-month {
    padding: 0.5rem;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-datepicker .p-monthpicker .p-monthpicker-month.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-datepicker .p-yearpicker {
    margin: 0.5rem 0;
  }
  .p-datepicker .p-yearpicker .p-yearpicker-year {
    padding: 0.5rem;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-datepicker .p-yearpicker .p-yearpicker-year.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group {
    border-left: 1px solid #263238;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
    padding-top: 0;
    padding-bottom: 0;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group:first-child {
    padding-left: 0;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group:last-child {
    padding-right: 0;
    border-left: 0 none;
  }
  .p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-datepicker:not(.p-disabled) .p-monthpicker .p-monthpicker-month:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-datepicker:not(.p-disabled) .p-monthpicker .p-monthpicker-month:not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-datepicker:not(.p-disabled) .p-yearpicker .p-yearpicker-year:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-datepicker:not(.p-disabled) .p-yearpicker .p-yearpicker-year:not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }

  @media screen and (max-width: 769px) {
    .p-datepicker table th, .p-datepicker table td {
      padding: 0;
    }
  }
  .p-cascadeselect {
    background: #0e1315;
    border: 2px solid #263238;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-cascadeselect:not(.p-disabled):hover {
    border-color: #2d3e44;
  }
  .p-cascadeselect:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-cascadeselect .p-cascadeselect-label {
    background: transparent;
    border: 0 none;
    padding: 0.5rem 0.75rem;
  }
  .p-cascadeselect .p-cascadeselect-label.p-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-cascadeselect .p-cascadeselect-label:enabled:focus {
    outline: 0 none;
    box-shadow: none;
  }
  .p-cascadeselect .p-cascadeselect-trigger {
    background: transparent;
    color: rgba(255, 255, 255, 0.6);
    width: 2.857rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-cascadeselect.p-invalid.p-component {
    border-color: #f78c79;
  }

  .p-cascadeselect-panel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 1px solid #263238;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-cascadeselect-panel .p-cascadeselect-items {
    padding: 0.5rem 0.5rem;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item {
    margin: 0 0 4px 0;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-item-content {
    padding: 0.5rem 1rem;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-item-content:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item:not(.p-highlight):not(.p-disabled):hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-group-icon {
    font-size: 0.875rem;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-group-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .p-input-filled .p-cascadeselect {
    background: #263238;
  }
  .p-input-filled .p-cascadeselect:not(.p-disabled):hover {
    background-color: #263238;
  }
  .p-input-filled .p-cascadeselect:not(.p-disabled).p-focus {
    background-color: #263238;
  }

  .p-checkbox {
    width: 20px;
    height: 20px;
  }
  .p-checkbox .p-checkbox-box {
    border: 2px solid #263238;
    background: #0e1315;
    width: 20px;
    height: 20px;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-checkbox .p-checkbox-box .p-checkbox-icon {
    transition-duration: 0.3s;
    color: #121212;
    font-size: 14px;
  }
  .p-checkbox .p-checkbox-box .p-checkbox-icon.p-icon {
    width: 14px;
    height: 14px;
  }
  .p-checkbox .p-checkbox-box.p-highlight {
    border-color: #9eade6;
    background: #9eade6;
  }
  .p-checkbox .p-checkbox-box.p-highlight:not(.p-disabled):hover {
    border-color: #7f93de;
    background: #7f93de;
    color: #121212;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #2d3e44;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    border-color: #7f93de;
    background: #7f93de;
    color: #121212;
  }
  .p-checkbox.p-invalid > .p-checkbox-box {
    border-color: #f78c79;
  }

  .p-input-filled .p-checkbox .p-checkbox-box {
    background-color: #263238;
  }
  .p-input-filled .p-checkbox .p-checkbox-box.p-highlight {
    background: #9eade6;
  }
  .p-input-filled .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    background-color: #263238;
  }
  .p-input-filled .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    background: #7f93de;
  }

  .p-chips .p-chips-multiple-container {
    padding: 0.25rem 0.75rem;
    gap: 0.5rem;
  }
  .p-chips .p-chips-multiple-container:not(.p-disabled):hover {
    border-color: #2d3e44;
  }
  .p-chips .p-chips-multiple-container:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-chips .p-chips-multiple-container .p-chips-token {
    padding: 0.25rem 0.75rem;
    margin-right: 0.5rem;
    background: rgba(158, 173, 230, 0.16);
    color: rgba(255, 255, 255, 0.87);
    border-radius: 16px;
  }
  .p-chips .p-chips-multiple-container .p-chips-token .p-chips-token-icon {
    margin-left: 0.5rem;
  }
  .p-chips .p-chips-multiple-container .p-chips-input-token {
    padding: 0.25rem 0;
  }
  .p-chips .p-chips-multiple-container .p-chips-input-token input {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.87);
    padding: 0;
    margin: 0;
  }
  .p-chips.p-invalid.p-component > .p-inputtext {
    border-color: #f78c79;
  }

  .p-colorpicker-preview {
    width: 2rem;
    height: 2rem;
  }

  .p-colorpicker-panel {
    background: #161d21;
    border: 1px solid #263238;
  }
  .p-colorpicker-panel .p-colorpicker-color-handle,
.p-colorpicker-panel .p-colorpicker-hue-handle {
    border-color: rgba(255, 255, 255, 0.87);
  }

  .p-colorpicker-overlay-panel {
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }

  .p-dropdown {
    background: #0e1315;
    border: 2px solid #263238;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-dropdown:not(.p-disabled):hover {
    border-color: #2d3e44;
  }
  .p-dropdown:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-dropdown.p-dropdown-clearable .p-dropdown-label {
    padding-right: 1.75rem;
  }
  .p-dropdown .p-dropdown-label {
    background: transparent;
    border: 0 none;
  }
  .p-dropdown .p-dropdown-label.p-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-dropdown .p-dropdown-label:enabled:focus {
    outline: 0 none;
    box-shadow: none;
  }
  .p-dropdown .p-dropdown-trigger {
    background: transparent;
    color: rgba(255, 255, 255, 0.6);
    width: 2.857rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-dropdown .p-dropdown-clear-icon {
    color: rgba(255, 255, 255, 0.6);
    right: 2.857rem;
  }
  .p-dropdown.p-invalid.p-component {
    border-color: #f78c79;
  }

  .p-dropdown-panel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 1px solid #263238;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-dropdown-panel .p-dropdown-header {
    padding: 0.5rem 1.5rem;
    border-bottom: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-dropdown-panel .p-dropdown-header .p-dropdown-filter {
    padding-right: 1.75rem;
    margin-right: -1.75rem;
  }
  .p-dropdown-panel .p-dropdown-header .p-dropdown-filter-icon {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-dropdown-panel .p-dropdown-header .p-dropdown-clearable-filter .p-dropdown-filter {
    padding-right: 3.5rem;
    margin-right: -3.5rem;
  }
  .p-dropdown-panel .p-dropdown-header .p-dropdown-clearable-filter .p-dropdown-filter-clear-icon {
    right: 2.5rem;
  }
  .p-dropdown-panel .p-dropdown-items {
    padding: 0.5rem 0.5rem;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item {
    margin: 0 0 4px 0;
    padding: 0.5rem 1rem;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item:not(.p-highlight):not(.p-disabled):hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item-group {
    margin: 0;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-empty-message {
    padding: 0.5rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
  }

  .p-input-filled .p-dropdown {
    background: #263238;
  }
  .p-input-filled .p-dropdown:not(.p-disabled):hover {
    background-color: #263238;
  }
  .p-input-filled .p-dropdown:not(.p-disabled).p-focus {
    background-color: #263238;
  }
  .p-input-filled .p-dropdown:not(.p-disabled).p-focus .p-inputtext {
    background-color: transparent;
  }

  .p-inputgroup-addon {
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    border-top: 2px solid #263238;
    border-left: 2px solid #263238;
    border-bottom: 2px solid #263238;
    padding: 0.5rem 0.75rem;
    min-width: 2.857rem;
  }
  .p-inputgroup-addon:last-child {
    border-right: 2px solid #263238;
  }

  .p-inputgroup > .p-component,
.p-inputgroup > .p-inputwrapper > .p-inputtext,
.p-inputgroup > .p-float-label > .p-component {
    border-radius: 0;
    margin: 0;
  }
  .p-inputgroup > .p-component + .p-inputgroup-addon,
.p-inputgroup > .p-inputwrapper > .p-inputtext + .p-inputgroup-addon,
.p-inputgroup > .p-float-label > .p-component + .p-inputgroup-addon {
    border-left: 0 none;
  }
  .p-inputgroup > .p-component:focus,
.p-inputgroup > .p-inputwrapper > .p-inputtext:focus,
.p-inputgroup > .p-float-label > .p-component:focus {
    z-index: 1;
  }
  .p-inputgroup > .p-component:focus ~ label,
.p-inputgroup > .p-inputwrapper > .p-inputtext:focus ~ label,
.p-inputgroup > .p-float-label > .p-component:focus ~ label {
    z-index: 1;
  }

  .p-inputgroup-addon:first-child,
.p-inputgroup button:first-child,
.p-inputgroup input:first-child,
.p-inputgroup > .p-inputwrapper:first-child,
.p-inputgroup > .p-inputwrapper:first-child > .p-inputtext {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-inputgroup .p-float-label:first-child input {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-inputgroup-addon:last-child,
.p-inputgroup button:last-child,
.p-inputgroup input:last-child,
.p-inputgroup > .p-inputwrapper:last-child,
.p-inputgroup > .p-inputwrapper:last-child > .p-inputtext {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .p-inputgroup .p-float-label:last-child input {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .p-fluid .p-inputgroup .p-button {
    width: auto;
  }
  .p-fluid .p-inputgroup .p-button.p-button-icon-only {
    width: 2.857rem;
  }

  .p-inputnumber.p-invalid.p-component > .p-inputtext {
    border-color: #f78c79;
  }

  .p-inputswitch {
    width: 3rem;
    height: 1.75rem;
  }
  .p-inputswitch .p-inputswitch-slider {
    background: #263238;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-inputswitch .p-inputswitch-slider:before {
    background: rgba(255, 255, 255, 0.6);
    width: 1.25rem;
    height: 1.25rem;
    left: 0.25rem;
    margin-top: -0.625rem;
    border-radius: 6px;
    transition-duration: 0.3s;
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before {
    transform: translateX(1.25rem);
  }
  .p-inputswitch.p-focus .p-inputswitch-slider {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-inputswitch:not(.p-disabled):hover .p-inputswitch-slider {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
    background: #9eade6;
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before {
    background: rgba(255, 255, 255, 0.87);
  }
  .p-inputswitch.p-inputswitch-checked:not(.p-disabled):hover .p-inputswitch-slider {
    background: #8fa0e2;
  }
  .p-inputswitch.p-invalid .p-inputswitch-slider {
    border-color: #f78c79;
  }

  .p-inputtext {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: #0e1315;
    padding: 0.5rem 0.75rem;
    border: 2px solid #263238;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    appearance: none;
    border-radius: 6px;
  }
  .p-inputtext:enabled:hover {
    border-color: #2d3e44;
  }
  .p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-inputtext.p-invalid.p-component {
    border-color: #f78c79;
  }
  .p-inputtext.p-inputtext-sm {
    font-size: 0.875rem;
    padding: 0.4375rem 0.65625rem;
  }
  .p-inputtext.p-inputtext-lg {
    font-size: 1.25rem;
    padding: 0.625rem 0.9375rem;
  }

  .p-float-label > label {
    left: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
    transition-duration: 0.3s;
  }

  .p-float-label > label.p-error {
    color: #f78c79;
  }

  .p-input-icon-left > i:first-of-type,
.p-input-icon-left > svg:first-of-type,
.p-input-icon-left > .p-input-prefix {
    left: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }

  .p-input-icon-left > .p-inputtext {
    padding-left: 2.5rem;
  }

  .p-input-icon-left.p-float-label > label {
    left: 2.5rem;
  }

  .p-input-icon-right > i:last-of-type,
.p-input-icon-right > svg:last-of-type,
.p-input-icon-right > .p-input-suffix {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }

  .p-input-icon-right > .p-inputtext {
    padding-right: 2.5rem;
  }

  ::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  :-moz-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  ::-moz-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  :-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  .p-input-filled .p-inputtext {
    background-color: #263238;
  }
  .p-input-filled .p-inputtext:enabled:hover {
    background-color: #263238;
  }
  .p-input-filled .p-inputtext:enabled:focus {
    background-color: #263238;
  }

  .p-inputtext-sm .p-inputtext {
    font-size: 0.875rem;
    padding: 0.4375rem 0.65625rem;
  }

  .p-inputtext-lg .p-inputtext {
    font-size: 1.25rem;
    padding: 0.625rem 0.9375rem;
  }

  .p-listbox {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
  }
  .p-listbox .p-listbox-header {
    padding: 0.5rem 1.5rem;
    border-bottom: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-listbox .p-listbox-header .p-listbox-filter {
    padding-right: 1.75rem;
  }
  .p-listbox .p-listbox-header .p-listbox-filter-icon {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-listbox .p-listbox-list {
    padding: 0.5rem 0.5rem;
  }
  .p-listbox .p-listbox-list .p-listbox-item {
    margin: 0 0 4px 0;
    padding: 0.5rem 1rem;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-listbox .p-listbox-list .p-listbox-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-listbox .p-listbox-list .p-listbox-item:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-listbox .p-listbox-list .p-listbox-item-group {
    margin: 0;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
  }
  .p-listbox .p-listbox-list .p-listbox-empty-message {
    padding: 0.5rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
  }
  .p-listbox:not(.p-disabled) .p-listbox-item:not(.p-highlight):not(.p-disabled):hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-listbox.p-invalid {
    border-color: #f78c79;
  }

  .p-mention-panel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 1px solid #263238;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-mention-panel .p-mention-items {
    padding: 0.5rem 0.5rem;
  }
  .p-mention-panel .p-mention-items .p-mention-item {
    margin: 0 0 4px 0;
    padding: 0.5rem 1rem;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-mention-panel .p-mention-items .p-mention-item:hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-mention-panel .p-mention-items .p-mention-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }

  .p-multiselect {
    background: #0e1315;
    border: 2px solid #263238;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-multiselect:not(.p-disabled):hover {
    border-color: #2d3e44;
  }
  .p-multiselect:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-multiselect.p-multiselect-clearable .p-multiselect-label {
    padding-right: 1.75rem;
  }
  .p-multiselect .p-multiselect-label {
    padding: 0.5rem 0.75rem;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-multiselect .p-multiselect-label.p-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token {
    padding: 0.25rem 0.75rem;
    margin-right: 0.5rem;
    background: #263238;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 16px;
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token .p-multiselect-token-icon {
    margin-left: 0.5rem;
  }
  .p-multiselect .p-multiselect-trigger {
    background: transparent;
    color: rgba(255, 255, 255, 0.6);
    width: 2.857rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-multiselect .p-multiselect-clear-icon {
    color: rgba(255, 255, 255, 0.6);
    right: 2.857rem;
  }
  .p-multiselect.p-invalid.p-component {
    border-color: #f78c79;
  }

  .p-inputwrapper-filled.p-multiselect.p-multiselect-chip .p-multiselect-label {
    padding: 0.25rem 0.75rem;
  }
  .p-inputwrapper-filled.p-multiselect.p-multiselect-chip .p-multiselect-label.p-multiselect-items-label {
    padding: 0.5rem 0.75rem;
  }
  .p-inputwrapper-filled.p-multiselect.p-multiselect-clearable .p-multiselect-label {
    padding-right: 1.75rem;
  }

  .p-multiselect-panel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 1px solid #263238;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-multiselect-panel .p-multiselect-header {
    padding: 0.5rem 1.5rem;
    border-bottom: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container {
    margin: 0 0.5rem;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-inputtext {
    padding-right: 1.75rem;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-multiselect-filter-icon {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-multiselect-panel .p-multiselect-items {
    padding: 0.5rem 0.5rem;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item {
    margin: 0 0 4px 0;
    padding: 0.5rem 1rem;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item:not(.p-highlight):not(.p-disabled):hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item-group {
    margin: 0;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-empty-message {
    padding: 0.5rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
  }

  .p-input-filled .p-multiselect {
    background: #263238;
  }
  .p-input-filled .p-multiselect:not(.p-disabled):hover {
    background-color: #263238;
  }
  .p-input-filled .p-multiselect:not(.p-disabled).p-focus {
    background-color: #263238;
  }

  .p-password.p-invalid.p-component > .p-inputtext {
    border-color: #f78c79;
  }

  .p-password-panel {
    padding: 1rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }
  .p-password-panel .p-password-meter {
    margin-bottom: 0.5rem;
    background: #263238;
  }
  .p-password-panel .p-password-meter .p-password-strength.weak {
    background: #e693a9;
  }
  .p-password-panel .p-password-meter .p-password-strength.medium {
    background: #ffe08a;
  }
  .p-password-panel .p-password-meter .p-password-strength.strong {
    background: #cede9c;
  }

  .p-radiobutton {
    width: 20px;
    height: 20px;
  }
  .p-radiobutton .p-radiobutton-box {
    border: 2px solid #263238;
    background: #0e1315;
    width: 20px;
    height: 20px;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-radiobutton .p-radiobutton-box:not(.p-disabled):not(.p-highlight):hover {
    border-color: #2d3e44;
  }
  .p-radiobutton .p-radiobutton-box:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
    width: 12px;
    height: 12px;
    transition-duration: 0.3s;
    background-color: #121212;
  }
  .p-radiobutton .p-radiobutton-box.p-highlight {
    border-color: #9eade6;
    background: #9eade6;
  }
  .p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    border-color: #7f93de;
    background: #7f93de;
    color: #121212;
  }
  .p-radiobutton.p-invalid > .p-radiobutton-box {
    border-color: #f78c79;
  }
  .p-radiobutton:focus {
    outline: 0 none;
  }

  .p-input-filled .p-radiobutton .p-radiobutton-box {
    background-color: #263238;
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box:not(.p-disabled):hover {
    background-color: #263238;
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box.p-highlight {
    background: #9eade6;
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    background: #7f93de;
  }

  .p-rating {
    gap: 0.5rem;
  }
  .p-rating .p-rating-item .p-rating-icon {
    color: rgba(255, 255, 255, 0.87);
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    font-size: 1.143rem;
  }
  .p-rating .p-rating-item .p-rating-icon.p-icon {
    width: 1.143rem;
    height: 1.143rem;
  }
  .p-rating .p-rating-item .p-rating-icon.p-rating-cancel {
    color: #dF7E6C;
  }
  .p-rating .p-rating-item:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-rating .p-rating-item.p-rating-item-active .p-rating-icon {
    color: #9eade6;
  }
  .p-rating:not(.p-disabled):not(.p-readonly) .p-rating-item:hover .p-rating-icon {
    color: #9eade6;
  }
  .p-rating:not(.p-disabled):not(.p-readonly) .p-rating-item:hover .p-rating-icon.p-rating-cancel {
    color: #f88c79;
  }

  .p-selectbutton .p-button {
    background: #161d21;
    border: 2px solid #263238;
    color: rgba(255, 255, 255, 0.87);
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-selectbutton .p-button .p-button-icon-left,
.p-selectbutton .p-button .p-button-icon-right {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    border-color: #263238;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-left,
.p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-right {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-selectbutton .p-button.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    border-color: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-selectbutton .p-button.p-highlight .p-button-icon-left,
.p-selectbutton .p-button.p-highlight .p-button-icon-right {
    color: #9eade6;
  }
  .p-selectbutton .p-button.p-highlight:hover {
    background: rgba(158, 173, 230, 0.24);
    border-color: rgba(158, 173, 230, 0.24);
    color: #9eade6;
  }
  .p-selectbutton .p-button.p-highlight:hover .p-button-icon-left,
.p-selectbutton .p-button.p-highlight:hover .p-button-icon-right {
    color: #9eade6;
  }
  .p-selectbutton.p-invalid > .p-button {
    border-color: #f78c79;
  }

  .p-slider {
    background: #263238;
    border: 0 none;
    border-radius: 6px;
  }
  .p-slider.p-slider-horizontal {
    height: 0.286rem;
  }
  .p-slider.p-slider-horizontal .p-slider-handle {
    margin-top: -0.5715rem;
    margin-left: -0.5715rem;
  }
  .p-slider.p-slider-vertical {
    width: 0.286rem;
  }
  .p-slider.p-slider-vertical .p-slider-handle {
    margin-left: -0.5715rem;
    margin-bottom: -0.5715rem;
  }
  .p-slider .p-slider-handle {
    height: 1.143rem;
    width: 1.143rem;
    background: #263238;
    border: 2px solid #9eade6;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-slider .p-slider-handle:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-slider .p-slider-range {
    background: #9eade6;
  }
  .p-slider:not(.p-disabled) .p-slider-handle:hover {
    background: #9eade6;
    border-color: #9eade6;
  }

  .p-treeselect {
    background: #0e1315;
    border: 2px solid #263238;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-treeselect:not(.p-disabled):hover {
    border-color: #2d3e44;
  }
  .p-treeselect:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: #9eade6;
  }
  .p-treeselect.p-treeselect-clearable .p-treeselect-label {
    padding-right: 1.75rem;
  }
  .p-treeselect .p-treeselect-label {
    padding: 0.5rem 0.75rem;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-treeselect .p-treeselect-label.p-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-treeselect.p-treeselect-chip .p-treeselect-token {
    padding: 0.25rem 0.75rem;
    margin-right: 0.5rem;
    background: #263238;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 16px;
  }
  .p-treeselect .p-treeselect-trigger {
    background: transparent;
    color: rgba(255, 255, 255, 0.6);
    width: 2.857rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-treeselect .p-treeselect-clear-icon {
    color: rgba(255, 255, 255, 0.6);
    right: 2.857rem;
  }
  .p-treeselect.p-invalid.p-component {
    border-color: #f78c79;
  }

  .p-inputwrapper-filled.p-treeselect.p-treeselect-chip .p-treeselect-label {
    padding: 0.25rem 0.75rem;
  }

  .p-treeselect-panel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 1px solid #263238;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-treeselect-panel .p-treeselect-header {
    padding: 0.5rem 1.5rem;
    border-bottom: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container {
    margin-right: 0.5rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container .p-treeselect-filter {
    padding-right: 1.75rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container .p-treeselect-filter-icon {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container.p-treeselect-clearable-filter .p-treeselect-filter {
    padding-right: 3.5rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container.p-treeselect-clearable-filter .p-treeselect-filter-clear-icon {
    right: 2.5rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-treeselect-panel .p-treeselect-items-wrapper .p-tree {
    border: 0 none;
  }
  .p-treeselect-panel .p-treeselect-items-wrapper .p-treeselect-empty-message {
    padding: 0.5rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
  }

  .p-input-filled .p-treeselect {
    background: #263238;
  }
  .p-input-filled .p-treeselect:not(.p-disabled):hover {
    background-color: #263238;
  }
  .p-input-filled .p-treeselect:not(.p-disabled).p-focus {
    background-color: #263238;
  }

  .p-togglebutton.p-button {
    background: #161d21;
    border: 2px solid #263238;
    color: rgba(255, 255, 255, 0.87);
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-togglebutton.p-button .p-button-icon-left,
.p-togglebutton.p-button .p-button-icon-right {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    border-color: #263238;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-left,
.p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-right {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-togglebutton.p-button.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    border-color: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-togglebutton.p-button.p-highlight .p-button-icon-left,
.p-togglebutton.p-button.p-highlight .p-button-icon-right {
    color: #9eade6;
  }
  .p-togglebutton.p-button.p-highlight:hover {
    background: rgba(158, 173, 230, 0.24);
    border-color: rgba(158, 173, 230, 0.24);
    color: #9eade6;
  }
  .p-togglebutton.p-button.p-highlight:hover .p-button-icon-left,
.p-togglebutton.p-button.p-highlight:hover .p-button-icon-right {
    color: #9eade6;
  }
  .p-togglebutton.p-button.p-invalid > .p-button {
    border-color: #f78c79;
  }

  .p-button {
    color: #121212;
    background: #9eade6;
    border: 2px solid #9eade6;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-button:not(:disabled):hover {
    background: #8fa0e2;
    color: #121212;
    border-color: #8fa0e2;
  }
  .p-button:not(:disabled):active {
    background: #7f93de;
    color: #121212;
    border-color: #7f93de;
  }
  .p-button.p-button-outlined {
    background-color: transparent;
    color: #9eade6;
    border: 2px solid;
  }
  .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.04);
    color: #9eade6;
    border: 2px solid;
  }
  .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
    border: 2px solid;
  }
  .p-button.p-button-outlined.p-button-plain {
    color: rgba(255, 255, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.6);
  }
  .p-button.p-button-outlined.p-button-plain:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-button.p-button-outlined.p-button-plain:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-button.p-button-text {
    background-color: transparent;
    color: #9eade6;
    border-color: transparent;
  }
  .p-button.p-button-text:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.04);
    color: #9eade6;
    border-color: transparent;
  }
  .p-button.p-button-text:not(:disabled):active {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
    border-color: transparent;
  }
  .p-button.p-button-text.p-button-plain {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-button.p-button-text.p-button-plain:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-button.p-button-text.p-button-plain:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-button:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-button .p-button-label {
    transition-duration: 0.3s;
  }
  .p-button .p-button-icon-left {
    margin-right: 0.5rem;
  }
  .p-button .p-button-icon-right {
    margin-left: 0.5rem;
  }
  .p-button .p-button-icon-bottom {
    margin-top: 0.5rem;
  }
  .p-button .p-button-icon-top {
    margin-bottom: 0.5rem;
  }
  .p-button .p-badge {
    margin-left: 0.5rem;
    min-width: 1rem;
    height: 1rem;
    line-height: 1rem;
    color: #9eade6;
    background-color: #121212;
  }
  .p-button.p-button-raised {
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  }
  .p-button.p-button-rounded {
    border-radius: 2rem;
  }
  .p-button.p-button-icon-only {
    width: 2.857rem;
    padding: 0.5rem 0;
  }
  .p-button.p-button-icon-only .p-button-icon-left,
.p-button.p-button-icon-only .p-button-icon-right {
    margin: 0;
  }
  .p-button.p-button-icon-only.p-button-rounded {
    border-radius: 50%;
    height: 2.857rem;
  }
  .p-button.p-button-sm {
    font-size: 0.875rem;
    padding: 0.4375rem 0.875rem;
  }
  .p-button.p-button-sm .p-button-icon {
    font-size: 0.875rem;
  }
  .p-button.p-button-lg {
    font-size: 1.25rem;
    padding: 0.625rem 1.25rem;
  }
  .p-button.p-button-lg .p-button-icon {
    font-size: 1.25rem;
  }
  .p-button.p-button-loading-label-only.p-button-loading-left .p-button-label {
    margin-left: 0.5rem;
  }
  .p-button.p-button-loading-label-only.p-button-loading-right .p-button-label {
    margin-right: 0.5rem;
  }
  .p-button.p-button-loading-label-only.p-button-loading-top .p-button-label {
    margin-top: 0.5rem;
  }
  .p-button.p-button-loading-label-only.p-button-loading-bottom .p-button-label {
    margin-bottom: 0.5rem;
  }
  .p-button.p-button-loading-label-only .p-button-loading-icon {
    margin: 0;
  }

  .p-fluid .p-button {
    width: 100%;
  }
  .p-fluid .p-button-icon-only {
    width: 2.857rem;
  }
  .p-fluid .p-buttonset {
    display: flex;
  }
  .p-fluid .p-buttonset .p-button {
    flex: 1;
  }

  .p-button.p-button-secondary, .p-buttonset.p-button-secondary > .p-button, .p-splitbutton.p-button-secondary > .p-button, .p-fileupload-choose.p-button-secondary {
    color: #0e1315;
    background: #b4bfcd;
    border: 1px solid #b4bfcd;
  }
  .p-button.p-button-secondary:not(:disabled):hover, .p-buttonset.p-button-secondary > .p-button:not(:disabled):hover, .p-splitbutton.p-button-secondary > .p-button:not(:disabled):hover, .p-fileupload-choose.p-button-secondary:not(:disabled):hover {
    background: #9dabbe;
    color: #0e1315;
    border-color: #9dabbe;
  }
  .p-button.p-button-secondary:not(:disabled):focus, .p-buttonset.p-button-secondary > .p-button:not(:disabled):focus, .p-splitbutton.p-button-secondary > .p-button:not(:disabled):focus, .p-fileupload-choose.p-button-secondary:not(:disabled):focus {
    box-shadow: 1px #e1e5eb;
  }
  .p-button.p-button-secondary:not(:disabled):active, .p-buttonset.p-button-secondary > .p-button:not(:disabled):active, .p-splitbutton.p-button-secondary > .p-button:not(:disabled):active, .p-fileupload-choose.p-button-secondary:not(:disabled):active {
    background: #8698ae;
    color: #0e1315;
    border-color: #8698ae;
  }
  .p-button.p-button-secondary.p-button-outlined, .p-buttonset.p-button-secondary > .p-button.p-button-outlined, .p-splitbutton.p-button-secondary > .p-button.p-button-outlined, .p-fileupload-choose.p-button-secondary.p-button-outlined {
    background-color: transparent;
    color: #b4bfcd;
    border: 2px solid;
  }
  .p-button.p-button-secondary.p-button-outlined:not(:disabled):hover, .p-buttonset.p-button-secondary > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-secondary > .p-button.p-button-outlined:not(:disabled):hover, .p-fileupload-choose.p-button-secondary.p-button-outlined:not(:disabled):hover {
    background: rgba(180, 191, 205, 0.04);
    color: #b4bfcd;
    border: 2px solid;
  }
  .p-button.p-button-secondary.p-button-outlined:not(:disabled):active, .p-buttonset.p-button-secondary > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-secondary > .p-button.p-button-outlined:not(:disabled):active, .p-fileupload-choose.p-button-secondary.p-button-outlined:not(:disabled):active {
    background: rgba(180, 191, 205, 0.16);
    color: #b4bfcd;
    border: 2px solid;
  }
  .p-button.p-button-secondary.p-button-text, .p-buttonset.p-button-secondary > .p-button.p-button-text, .p-splitbutton.p-button-secondary > .p-button.p-button-text, .p-fileupload-choose.p-button-secondary.p-button-text {
    background-color: transparent;
    color: #b4bfcd;
    border-color: transparent;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):hover, .p-buttonset.p-button-secondary > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-secondary > .p-button.p-button-text:not(:disabled):hover, .p-fileupload-choose.p-button-secondary.p-button-text:not(:disabled):hover {
    background: rgba(180, 191, 205, 0.04);
    border-color: transparent;
    color: #b4bfcd;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):active, .p-buttonset.p-button-secondary > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-secondary > .p-button.p-button-text:not(:disabled):active, .p-fileupload-choose.p-button-secondary.p-button-text:not(:disabled):active {
    background: rgba(180, 191, 205, 0.16);
    border-color: transparent;
    color: #b4bfcd;
  }

  .p-button.p-button-info, .p-buttonset.p-button-info > .p-button, .p-splitbutton.p-button-info > .p-button, .p-fileupload-choose.p-button-info {
    color: #ffffff;
    background: #35a4cc;
    border: 1px solid #35a4cc;
  }
  .p-button.p-button-info:not(:disabled):hover, .p-buttonset.p-button-info > .p-button:not(:disabled):hover, .p-splitbutton.p-button-info > .p-button:not(:disabled):hover, .p-fileupload-choose.p-button-info:not(:disabled):hover {
    background: #2f94b9;
    color: #ffffff;
    border-color: #2f94b9;
  }
  .p-button.p-button-info:not(:disabled):focus, .p-buttonset.p-button-info > .p-button:not(:disabled):focus, .p-splitbutton.p-button-info > .p-button:not(:disabled):focus, .p-fileupload-choose.p-button-info:not(:disabled):focus {
    box-shadow: 0 0 0 1px #aedbeb;
  }
  .p-button.p-button-info:not(:disabled):active, .p-buttonset.p-button-info > .p-button:not(:disabled):active, .p-splitbutton.p-button-info > .p-button:not(:disabled):active, .p-fileupload-choose.p-button-info:not(:disabled):active {
    background: #2984a4;
    color: #ffffff;
    border-color: #2984a4;
  }
  .p-button.p-button-info.p-button-outlined, .p-buttonset.p-button-info > .p-button.p-button-outlined, .p-splitbutton.p-button-info > .p-button.p-button-outlined, .p-fileupload-choose.p-button-info.p-button-outlined {
    background-color: transparent;
    color: #35a4cc;
    border: 2px solid;
  }
  .p-button.p-button-info.p-button-outlined:not(:disabled):hover, .p-buttonset.p-button-info > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-info > .p-button.p-button-outlined:not(:disabled):hover, .p-fileupload-choose.p-button-info.p-button-outlined:not(:disabled):hover {
    background: rgba(53, 164, 204, 0.04);
    color: #35a4cc;
    border: 2px solid;
  }
  .p-button.p-button-info.p-button-outlined:not(:disabled):active, .p-buttonset.p-button-info > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-info > .p-button.p-button-outlined:not(:disabled):active, .p-fileupload-choose.p-button-info.p-button-outlined:not(:disabled):active {
    background: rgba(53, 164, 204, 0.16);
    color: #35a4cc;
    border: 2px solid;
  }
  .p-button.p-button-info.p-button-text, .p-buttonset.p-button-info > .p-button.p-button-text, .p-splitbutton.p-button-info > .p-button.p-button-text, .p-fileupload-choose.p-button-info.p-button-text {
    background-color: transparent;
    color: #35a4cc;
    border-color: transparent;
  }
  .p-button.p-button-info.p-button-text:not(:disabled):hover, .p-buttonset.p-button-info > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-info > .p-button.p-button-text:not(:disabled):hover, .p-fileupload-choose.p-button-info.p-button-text:not(:disabled):hover {
    background: rgba(53, 164, 204, 0.04);
    border-color: transparent;
    color: #35a4cc;
  }
  .p-button.p-button-info.p-button-text:not(:disabled):active, .p-buttonset.p-button-info > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-info > .p-button.p-button-text:not(:disabled):active, .p-fileupload-choose.p-button-info.p-button-text:not(:disabled):active {
    background: rgba(53, 164, 204, 0.16);
    border-color: transparent;
    color: #35a4cc;
  }

  .p-button.p-button-success, .p-buttonset.p-button-success > .p-button, .p-splitbutton.p-button-success > .p-button, .p-fileupload-choose.p-button-success {
    color: #0e1315;
    background: #cede9c;
    border: 1px solid #cede9c;
  }
  .p-button.p-button-success:not(:disabled):hover, .p-buttonset.p-button-success > .p-button:not(:disabled):hover, .p-splitbutton.p-button-success > .p-button:not(:disabled):hover, .p-fileupload-choose.p-button-success:not(:disabled):hover {
    background: #c0d580;
    color: #0e1315;
    border-color: #c0d580;
  }
  .p-button.p-button-success:not(:disabled):focus, .p-buttonset.p-button-success > .p-button:not(:disabled):focus, .p-splitbutton.p-button-success > .p-button:not(:disabled):focus, .p-fileupload-choose.p-button-success:not(:disabled):focus {
    box-shadow: 0 0 0 1px #ebf2d7;
  }
  .p-button.p-button-success:not(:disabled):active, .p-buttonset.p-button-success > .p-button:not(:disabled):active, .p-splitbutton.p-button-success > .p-button:not(:disabled):active, .p-fileupload-choose.p-button-success:not(:disabled):active {
    background: #b2cb63;
    color: #0e1315;
    border-color: #b2cb63;
  }
  .p-button.p-button-success.p-button-outlined, .p-buttonset.p-button-success > .p-button.p-button-outlined, .p-splitbutton.p-button-success > .p-button.p-button-outlined, .p-fileupload-choose.p-button-success.p-button-outlined {
    background-color: transparent;
    color: #cede9c;
    border: 2px solid;
  }
  .p-button.p-button-success.p-button-outlined:not(:disabled):hover, .p-buttonset.p-button-success > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-success > .p-button.p-button-outlined:not(:disabled):hover, .p-fileupload-choose.p-button-success.p-button-outlined:not(:disabled):hover {
    background: rgba(206, 222, 156, 0.04);
    color: #cede9c;
    border: 2px solid;
  }
  .p-button.p-button-success.p-button-outlined:not(:disabled):active, .p-buttonset.p-button-success > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-success > .p-button.p-button-outlined:not(:disabled):active, .p-fileupload-choose.p-button-success.p-button-outlined:not(:disabled):active {
    background: rgba(206, 222, 156, 0.16);
    color: #cede9c;
    border: 2px solid;
  }
  .p-button.p-button-success.p-button-text, .p-buttonset.p-button-success > .p-button.p-button-text, .p-splitbutton.p-button-success > .p-button.p-button-text, .p-fileupload-choose.p-button-success.p-button-text {
    background-color: transparent;
    color: #cede9c;
    border-color: transparent;
  }
  .p-button.p-button-success.p-button-text:not(:disabled):hover, .p-buttonset.p-button-success > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-success > .p-button.p-button-text:not(:disabled):hover, .p-fileupload-choose.p-button-success.p-button-text:not(:disabled):hover {
    background: rgba(206, 222, 156, 0.04);
    border-color: transparent;
    color: #cede9c;
  }
  .p-button.p-button-success.p-button-text:not(:disabled):active, .p-buttonset.p-button-success > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-success > .p-button.p-button-text:not(:disabled):active, .p-fileupload-choose.p-button-success.p-button-text:not(:disabled):active {
    background: rgba(206, 222, 156, 0.16);
    border-color: transparent;
    color: #cede9c;
  }

  .p-button.p-button-warning, .p-buttonset.p-button-warning > .p-button, .p-splitbutton.p-button-warning > .p-button, .p-fileupload-choose.p-button-warning {
    color: #0e1315;
    background: #ffe08a;
    border: 1px solid #ffe08a;
  }
  .p-button.p-button-warning:not(:disabled):hover, .p-buttonset.p-button-warning > .p-button:not(:disabled):hover, .p-splitbutton.p-button-warning > .p-button:not(:disabled):hover, .p-fileupload-choose.p-button-warning:not(:disabled):hover {
    background: #ffd663;
    color: #0e1315;
    border-color: #ffd663;
  }
  .p-button.p-button-warning:not(:disabled):focus, .p-buttonset.p-button-warning > .p-button:not(:disabled):focus, .p-splitbutton.p-button-warning > .p-button:not(:disabled):focus, .p-fileupload-choose.p-button-warning:not(:disabled):focus {
    box-shadow: 0 0 0 1px #fff3d0;
  }
  .p-button.p-button-warning:not(:disabled):active, .p-buttonset.p-button-warning > .p-button:not(:disabled):active, .p-splitbutton.p-button-warning > .p-button:not(:disabled):active, .p-fileupload-choose.p-button-warning:not(:disabled):active {
    background: #ffcb3b;
    color: #0e1315;
    border-color: #ffcb3b;
  }
  .p-button.p-button-warning.p-button-outlined, .p-buttonset.p-button-warning > .p-button.p-button-outlined, .p-splitbutton.p-button-warning > .p-button.p-button-outlined, .p-fileupload-choose.p-button-warning.p-button-outlined {
    background-color: transparent;
    color: #ffe08a;
    border: 2px solid;
  }
  .p-button.p-button-warning.p-button-outlined:not(:disabled):hover, .p-buttonset.p-button-warning > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-warning > .p-button.p-button-outlined:not(:disabled):hover, .p-fileupload-choose.p-button-warning.p-button-outlined:not(:disabled):hover {
    background: rgba(255, 224, 138, 0.04);
    color: #ffe08a;
    border: 2px solid;
  }
  .p-button.p-button-warning.p-button-outlined:not(:disabled):active, .p-buttonset.p-button-warning > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-warning > .p-button.p-button-outlined:not(:disabled):active, .p-fileupload-choose.p-button-warning.p-button-outlined:not(:disabled):active {
    background: rgba(255, 224, 138, 0.16);
    color: #ffe08a;
    border: 2px solid;
  }
  .p-button.p-button-warning.p-button-text, .p-buttonset.p-button-warning > .p-button.p-button-text, .p-splitbutton.p-button-warning > .p-button.p-button-text, .p-fileupload-choose.p-button-warning.p-button-text {
    background-color: transparent;
    color: #ffe08a;
    border-color: transparent;
  }
  .p-button.p-button-warning.p-button-text:not(:disabled):hover, .p-buttonset.p-button-warning > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-warning > .p-button.p-button-text:not(:disabled):hover, .p-fileupload-choose.p-button-warning.p-button-text:not(:disabled):hover {
    background: rgba(255, 224, 138, 0.04);
    border-color: transparent;
    color: #ffe08a;
  }
  .p-button.p-button-warning.p-button-text:not(:disabled):active, .p-buttonset.p-button-warning > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-warning > .p-button.p-button-text:not(:disabled):active, .p-fileupload-choose.p-button-warning.p-button-text:not(:disabled):active {
    background: rgba(255, 224, 138, 0.16);
    border-color: transparent;
    color: #ffe08a;
  }

  .p-button.p-button-help, .p-buttonset.p-button-help > .p-button, .p-splitbutton.p-button-help > .p-button, .p-fileupload-choose.p-button-help {
    color: #0e1315;
    background: #b09ce5;
    border: 1px solid #b09ce5;
  }
  .p-button.p-button-help:not(:disabled):hover, .p-buttonset.p-button-help > .p-button:not(:disabled):hover, .p-splitbutton.p-button-help > .p-button:not(:disabled):hover, .p-fileupload-choose.p-button-help:not(:disabled):hover {
    background: #987edd;
    color: #0e1315;
    border-color: #987edd;
  }
  .p-button.p-button-help:not(:disabled):focus, .p-buttonset.p-button-help > .p-button:not(:disabled):focus, .p-splitbutton.p-button-help > .p-button:not(:disabled):focus, .p-fileupload-choose.p-button-help:not(:disabled):focus {
    box-shadow: 0 0 0 1px #dfd7f5;
  }
  .p-button.p-button-help:not(:disabled):active, .p-buttonset.p-button-help > .p-button:not(:disabled):active, .p-splitbutton.p-button-help > .p-button:not(:disabled):active, .p-fileupload-choose.p-button-help:not(:disabled):active {
    background: #7f5fd5;
    color: #0e1315;
    border-color: #7f5fd5;
  }
  .p-button.p-button-help.p-button-outlined, .p-buttonset.p-button-help > .p-button.p-button-outlined, .p-splitbutton.p-button-help > .p-button.p-button-outlined, .p-fileupload-choose.p-button-help.p-button-outlined {
    background-color: transparent;
    color: #b09ce5;
    border: 2px solid;
  }
  .p-button.p-button-help.p-button-outlined:not(:disabled):hover, .p-buttonset.p-button-help > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-help > .p-button.p-button-outlined:not(:disabled):hover, .p-fileupload-choose.p-button-help.p-button-outlined:not(:disabled):hover {
    background: rgba(176, 156, 229, 0.04);
    color: #b09ce5;
    border: 2px solid;
  }
  .p-button.p-button-help.p-button-outlined:not(:disabled):active, .p-buttonset.p-button-help > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-help > .p-button.p-button-outlined:not(:disabled):active, .p-fileupload-choose.p-button-help.p-button-outlined:not(:disabled):active {
    background: rgba(176, 156, 229, 0.16);
    color: #b09ce5;
    border: 2px solid;
  }
  .p-button.p-button-help.p-button-text, .p-buttonset.p-button-help > .p-button.p-button-text, .p-splitbutton.p-button-help > .p-button.p-button-text, .p-fileupload-choose.p-button-help.p-button-text {
    background-color: transparent;
    color: #b09ce5;
    border-color: transparent;
  }
  .p-button.p-button-help.p-button-text:not(:disabled):hover, .p-buttonset.p-button-help > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-help > .p-button.p-button-text:not(:disabled):hover, .p-fileupload-choose.p-button-help.p-button-text:not(:disabled):hover {
    background: rgba(176, 156, 229, 0.04);
    border-color: transparent;
    color: #b09ce5;
  }
  .p-button.p-button-help.p-button-text:not(:disabled):active, .p-buttonset.p-button-help > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-help > .p-button.p-button-text:not(:disabled):active, .p-fileupload-choose.p-button-help.p-button-text:not(:disabled):active {
    background: rgba(176, 156, 229, 0.16);
    border-color: transparent;
    color: #b09ce5;
  }

  .p-button.p-button-danger, .p-buttonset.p-button-danger > .p-button, .p-splitbutton.p-button-danger > .p-button, .p-fileupload-choose.p-button-danger {
    color: #0e1315;
    background: #e693a9;
    border: 1px solid #e693a9;
  }
  .p-button.p-button-danger:not(:disabled):hover, .p-buttonset.p-button-danger > .p-button:not(:disabled):hover, .p-splitbutton.p-button-danger > .p-button:not(:disabled):hover, .p-fileupload-choose.p-button-danger:not(:disabled):hover {
    background: #df7491;
    color: #0e1315;
    border-color: #df7491;
  }
  .p-button.p-button-danger:not(:disabled):focus, .p-buttonset.p-button-danger > .p-button:not(:disabled):focus, .p-splitbutton.p-button-danger > .p-button:not(:disabled):focus, .p-fileupload-choose.p-button-danger:not(:disabled):focus {
    box-shadow: 0 0 0 1px #f5d4dd;
  }
  .p-button.p-button-danger:not(:disabled):active, .p-buttonset.p-button-danger > .p-button:not(:disabled):active, .p-splitbutton.p-button-danger > .p-button:not(:disabled):active, .p-fileupload-choose.p-button-danger:not(:disabled):active {
    background: #d85678;
    color: #0e1315;
    border-color: #d85678;
  }
  .p-button.p-button-danger.p-button-outlined, .p-buttonset.p-button-danger > .p-button.p-button-outlined, .p-splitbutton.p-button-danger > .p-button.p-button-outlined, .p-fileupload-choose.p-button-danger.p-button-outlined {
    background-color: transparent;
    color: #e693a9;
    border: 2px solid;
  }
  .p-button.p-button-danger.p-button-outlined:not(:disabled):hover, .p-buttonset.p-button-danger > .p-button.p-button-outlined:not(:disabled):hover, .p-splitbutton.p-button-danger > .p-button.p-button-outlined:not(:disabled):hover, .p-fileupload-choose.p-button-danger.p-button-outlined:not(:disabled):hover {
    background: rgba(230, 147, 169, 0.04);
    color: #e693a9;
    border: 2px solid;
  }
  .p-button.p-button-danger.p-button-outlined:not(:disabled):active, .p-buttonset.p-button-danger > .p-button.p-button-outlined:not(:disabled):active, .p-splitbutton.p-button-danger > .p-button.p-button-outlined:not(:disabled):active, .p-fileupload-choose.p-button-danger.p-button-outlined:not(:disabled):active {
    background: rgba(230, 147, 169, 0.16);
    color: #e693a9;
    border: 2px solid;
  }
  .p-button.p-button-danger.p-button-text, .p-buttonset.p-button-danger > .p-button.p-button-text, .p-splitbutton.p-button-danger > .p-button.p-button-text, .p-fileupload-choose.p-button-danger.p-button-text {
    background-color: transparent;
    color: #e693a9;
    border-color: transparent;
  }
  .p-button.p-button-danger.p-button-text:not(:disabled):hover, .p-buttonset.p-button-danger > .p-button.p-button-text:not(:disabled):hover, .p-splitbutton.p-button-danger > .p-button.p-button-text:not(:disabled):hover, .p-fileupload-choose.p-button-danger.p-button-text:not(:disabled):hover {
    background: rgba(230, 147, 169, 0.04);
    border-color: transparent;
    color: #e693a9;
  }
  .p-button.p-button-danger.p-button-text:not(:disabled):active, .p-buttonset.p-button-danger > .p-button.p-button-text:not(:disabled):active, .p-splitbutton.p-button-danger > .p-button.p-button-text:not(:disabled):active, .p-fileupload-choose.p-button-danger.p-button-text:not(:disabled):active {
    background: rgba(230, 147, 169, 0.16);
    border-color: transparent;
    color: #e693a9;
  }

  .p-button.p-button-link {
    color: #7f93de;
    background: transparent;
    border: transparent;
  }
  .p-button.p-button-link:not(:disabled):hover {
    background: transparent;
    color: #7f93de;
    border-color: transparent;
  }
  .p-button.p-button-link:not(:disabled):hover .p-button-label {
    text-decoration: underline;
  }
  .p-button.p-button-link:not(:disabled):focus {
    background: transparent;
    box-shadow: 0 0 0 1px #9eade6;
    border-color: transparent;
  }
  .p-button.p-button-link:not(:disabled):active {
    background: transparent;
    color: #7f93de;
    border-color: transparent;
  }

  .p-splitbutton {
    border-radius: 6px;
  }
  .p-splitbutton.p-button-outlined > .p-button {
    background-color: transparent;
    color: #9eade6;
    border: 2px solid;
  }
  .p-splitbutton.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.04);
    color: #9eade6;
  }
  .p-splitbutton.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-splitbutton.p-button-outlined.p-button-plain > .p-button {
    color: rgba(255, 255, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.6);
  }
  .p-splitbutton.p-button-outlined.p-button-plain > .p-button:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-splitbutton.p-button-outlined.p-button-plain > .p-button:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-splitbutton.p-button-text > .p-button {
    background-color: transparent;
    color: #9eade6;
    border-color: transparent;
  }
  .p-splitbutton.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.04);
    color: #9eade6;
    border-color: transparent;
  }
  .p-splitbutton.p-button-text > .p-button:not(:disabled):active {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
    border-color: transparent;
  }
  .p-splitbutton.p-button-text.p-button-plain > .p-button {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-splitbutton.p-button-text.p-button-plain > .p-button:not(:disabled):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-splitbutton.p-button-text.p-button-plain > .p-button:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: rgba(255, 255, 255, 0.6);
  }
  .p-splitbutton.p-button-raised {
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  }
  .p-splitbutton.p-button-rounded {
    border-radius: 2rem;
  }
  .p-splitbutton.p-button-rounded > .p-button {
    border-radius: 2rem;
  }
  .p-splitbutton.p-button-sm > .p-button {
    font-size: 0.875rem;
    padding: 0.4375rem 0.875rem;
  }
  .p-splitbutton.p-button-sm > .p-button .p-button-icon {
    font-size: 0.875rem;
  }
  .p-splitbutton.p-button-lg > .p-button {
    font-size: 1.25rem;
    padding: 0.625rem 1.25rem;
  }
  .p-splitbutton.p-button-lg > .p-button.p-button-icon-only {
    width: auto;
  }
  .p-splitbutton.p-button-lg > .p-button .p-button-icon {
    font-size: 1.25rem;
  }
  .p-splitbutton .p-splitbutton-menubutton,
.p-splitbutton .p-splitbutton.p-button-rounded > .p-splitbutton-menubutton.p-button,
.p-splitbutton .p-splitbutton.p-button-outlined > .p-splitbutton-menubutton.p-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .p-splitbutton.p-button-secondary.p-button-outlined > .p-button {
    background-color: transparent;
    color: #b4bfcd;
    border: 2px solid;
  }
  .p-splitbutton.p-button-secondary.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(180, 191, 205, 0.04);
    color: #b4bfcd;
  }
  .p-splitbutton.p-button-secondary.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(180, 191, 205, 0.16);
    color: #b4bfcd;
  }
  .p-splitbutton.p-button-secondary.p-button-text > .p-button {
    background-color: transparent;
    color: #b4bfcd;
    border-color: transparent;
  }
  .p-splitbutton.p-button-secondary.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(180, 191, 205, 0.04);
    border-color: transparent;
    color: #b4bfcd;
  }
  .p-splitbutton.p-button-secondary.p-button-text > .p-button:not(:disabled):active {
    background: rgba(180, 191, 205, 0.16);
    border-color: transparent;
    color: #b4bfcd;
  }

  .p-splitbutton.p-button-info.p-button-outlined > .p-button {
    background-color: transparent;
    color: #35a4cc;
    border: 2px solid;
  }
  .p-splitbutton.p-button-info.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(53, 164, 204, 0.04);
    color: #35a4cc;
  }
  .p-splitbutton.p-button-info.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(53, 164, 204, 0.16);
    color: #35a4cc;
  }
  .p-splitbutton.p-button-info.p-button-text > .p-button {
    background-color: transparent;
    color: #35a4cc;
    border-color: transparent;
  }
  .p-splitbutton.p-button-info.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(53, 164, 204, 0.04);
    border-color: transparent;
    color: #35a4cc;
  }
  .p-splitbutton.p-button-info.p-button-text > .p-button:not(:disabled):active {
    background: rgba(53, 164, 204, 0.16);
    border-color: transparent;
    color: #35a4cc;
  }

  .p-splitbutton.p-button-success.p-button-outlined > .p-button {
    background-color: transparent;
    color: #cede9c;
    border: 2px solid;
  }
  .p-splitbutton.p-button-success.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(206, 222, 156, 0.04);
    color: #cede9c;
  }
  .p-splitbutton.p-button-success.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(206, 222, 156, 0.16);
    color: #cede9c;
  }
  .p-splitbutton.p-button-success.p-button-text > .p-button {
    background-color: transparent;
    color: #cede9c;
    border-color: transparent;
  }
  .p-splitbutton.p-button-success.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(206, 222, 156, 0.04);
    border-color: transparent;
    color: #cede9c;
  }
  .p-splitbutton.p-button-success.p-button-text > .p-button:not(:disabled):active {
    background: rgba(206, 222, 156, 0.16);
    border-color: transparent;
    color: #cede9c;
  }

  .p-splitbutton.p-button-warning.p-button-outlined > .p-button {
    background-color: transparent;
    color: #ffe08a;
    border: 2px solid;
  }
  .p-splitbutton.p-button-warning.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(255, 224, 138, 0.04);
    color: #ffe08a;
  }
  .p-splitbutton.p-button-warning.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(255, 224, 138, 0.16);
    color: #ffe08a;
  }
  .p-splitbutton.p-button-warning.p-button-text > .p-button {
    background-color: transparent;
    color: #ffe08a;
    border-color: transparent;
  }
  .p-splitbutton.p-button-warning.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(255, 224, 138, 0.04);
    border-color: transparent;
    color: #ffe08a;
  }
  .p-splitbutton.p-button-warning.p-button-text > .p-button:not(:disabled):active {
    background: rgba(255, 224, 138, 0.16);
    border-color: transparent;
    color: #ffe08a;
  }

  .p-splitbutton.p-button-help.p-button-outlined > .p-button {
    background-color: transparent;
    color: #b09ce5;
    border: 2px solid;
  }
  .p-splitbutton.p-button-help.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(176, 156, 229, 0.04);
    color: #b09ce5;
  }
  .p-splitbutton.p-button-help.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(176, 156, 229, 0.16);
    color: #b09ce5;
  }
  .p-splitbutton.p-button-help.p-button-text > .p-button {
    background-color: transparent;
    color: #b09ce5;
    border-color: transparent;
  }
  .p-splitbutton.p-button-help.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(176, 156, 229, 0.04);
    border-color: transparent;
    color: #b09ce5;
  }
  .p-splitbutton.p-button-help.p-button-text > .p-button:not(:disabled):active {
    background: rgba(176, 156, 229, 0.16);
    border-color: transparent;
    color: #b09ce5;
  }

  .p-splitbutton.p-button-danger.p-button-outlined > .p-button {
    background-color: transparent;
    color: #e693a9;
    border: 2px solid;
  }
  .p-splitbutton.p-button-danger.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(230, 147, 169, 0.04);
    color: #e693a9;
  }
  .p-splitbutton.p-button-danger.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(230, 147, 169, 0.16);
    color: #e693a9;
  }
  .p-splitbutton.p-button-danger.p-button-text > .p-button {
    background-color: transparent;
    color: #e693a9;
    border-color: transparent;
  }
  .p-splitbutton.p-button-danger.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(230, 147, 169, 0.04);
    border-color: transparent;
    color: #e693a9;
  }
  .p-splitbutton.p-button-danger.p-button-text > .p-button:not(:disabled):active {
    background: rgba(230, 147, 169, 0.16);
    border-color: transparent;
    color: #e693a9;
  }

  .p-speeddial-button.p-button.p-button-icon-only {
    width: 4rem;
    height: 4rem;
  }
  .p-speeddial-button.p-button.p-button-icon-only .p-button-icon {
    font-size: 1.3rem;
  }
  .p-speeddial-button.p-button.p-button-icon-only .p-button-icon.p-icon {
    width: 1.3rem;
    height: 1.3rem;
  }

  .p-speeddial-action {
    width: 3rem;
    height: 3rem;
    background: #1e282c;
    color: #fff;
  }
  .p-speeddial-action:hover {
    background: #263238;
    color: #fff;
  }

  .p-speeddial-direction-up .p-speeddial-item {
    margin: 0.25rem;
  }
  .p-speeddial-direction-up .p-speeddial-item:first-child {
    margin-bottom: 0.5rem;
  }

  .p-speeddial-direction-down .p-speeddial-item {
    margin: 0.25rem;
  }
  .p-speeddial-direction-down .p-speeddial-item:first-child {
    margin-top: 0.5rem;
  }

  .p-speeddial-direction-left .p-speeddial-item {
    margin: 0 0.25rem;
  }
  .p-speeddial-direction-left .p-speeddial-item:first-child {
    margin-right: 0.5rem;
  }

  .p-speeddial-direction-right .p-speeddial-item {
    margin: 0 0.25rem;
  }
  .p-speeddial-direction-right .p-speeddial-item:first-child {
    margin-left: 0.5rem;
  }

  .p-speeddial-circle .p-speeddial-item,
.p-speeddial-semi-circle .p-speeddial-item,
.p-speeddial-quarter-circle .p-speeddial-item {
    margin: 0;
  }
  .p-speeddial-circle .p-speeddial-item:first-child, .p-speeddial-circle .p-speeddial-item:last-child,
.p-speeddial-semi-circle .p-speeddial-item:first-child,
.p-speeddial-semi-circle .p-speeddial-item:last-child,
.p-speeddial-quarter-circle .p-speeddial-item:first-child,
.p-speeddial-quarter-circle .p-speeddial-item:last-child {
    margin: 0;
  }

  .p-speeddial-mask {
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 6px;
  }

  .p-carousel .p-carousel-content .p-carousel-prev,
.p-carousel .p-carousel-content .p-carousel-next {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    margin: 0.5rem;
  }
  .p-carousel .p-carousel-content .p-carousel-prev:enabled:hover,
.p-carousel .p-carousel-content .p-carousel-next:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-carousel .p-carousel-content .p-carousel-prev:focus-visible,
.p-carousel .p-carousel-content .p-carousel-next:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-carousel .p-carousel-indicators {
    padding: 1rem;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator button {
    background-color: #263238;
    width: 2rem;
    height: 0.5rem;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    border-radius: 0;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator button:hover {
    background: #374851;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator.p-highlight button {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }

  .p-datatable .p-paginator-top {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-datatable .p-paginator-bottom {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-datatable .p-datatable-header {
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
  }
  .p-datatable .p-datatable-footer {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
  }
  .p-datatable .p-datatable-thead > tr > th {
    text-align: left;
    padding: 1rem 1rem;
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    transition: box-shadow 0.3s;
  }
  .p-datatable .p-datatable-tfoot > tr > td {
    text-align: left;
    padding: 1rem 1rem;
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
  }
  .p-datatable .p-sortable-column .p-sortable-column-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-left: 0.5rem;
  }
  .p-datatable .p-sortable-column .p-sortable-column-badge {
    border-radius: 50%;
    height: 1.143rem;
    min-width: 1.143rem;
    line-height: 1.143rem;
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
    margin-left: 0.5rem;
  }
  .p-datatable .p-sortable-column:not(.p-highlight):not(.p-sortable-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-datatable .p-sortable-column:not(.p-highlight):not(.p-sortable-disabled):hover .p-sortable-column-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-datatable .p-sortable-column.p-highlight {
    background: #161d21;
    color: #9eade6;
  }
  .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #9eade6;
  }
  .p-datatable .p-sortable-column.p-highlight:not(.p-sortable-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
    color: #9eade6;
  }
  .p-datatable .p-sortable-column.p-highlight:not(.p-sortable-disabled):hover .p-sortable-column-icon {
    color: #9eade6;
  }
  .p-datatable .p-sortable-column:focus-visible {
    box-shadow: inset 0 0 0 0.15rem #9eade6;
    outline: 0 none;
  }
  .p-datatable .p-datatable-tbody > tr {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    transition: box-shadow 0.3s;
  }
  .p-datatable .p-datatable-tbody > tr > td {
    text-align: left;
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-editor-save {
    margin-right: 0.5rem;
  }
  .p-datatable .p-datatable-tbody > tr > td > .p-column-title {
    font-weight: 600;
  }
  .p-datatable .p-datatable-tbody > tr > td.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-datatable .p-datatable-tbody > tr.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-datatable .p-datatable-tbody > tr.p-highlight-contextmenu {
    outline: 0.15rem solid #9eade6;
    outline-offset: -0.15rem;
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-top > td {
    box-shadow: inset 0 2px 0 0 rgba(158, 173, 230, 0.16);
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {
    box-shadow: inset 0 -2px 0 0 rgba(158, 173, 230, 0.16);
  }
  .p-datatable.p-datatable-selectable .p-datatable-tbody > tr.p-selectable-row:not(.p-highlight):not(.p-datatable-emptymessage):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-datatable.p-datatable-selectable .p-datatable-tbody > tr.p-selectable-row:focus-visible {
    outline: 0.15rem solid #9eade6;
    outline-offset: -0.15rem;
  }
  .p-datatable.p-datatable-selectable-cell .p-datatable-tbody > tr.p-selectable-row > td.p-selectable-cell:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-datatable.p-datatable-selectable-cell .p-datatable-tbody > tr.p-selectable-row > td.p-selectable-cell:focus-visible {
    outline: 0.15rem solid #9eade6;
    outline-offset: -0.15rem;
  }
  .p-datatable.p-datatable-hoverable-rows .p-datatable-tbody > tr:not(.p-highlight):not(.p-datatable-emptymessage):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-datatable .p-column-resizer-helper {
    background: #9eade6;
  }
  .p-datatable .p-datatable-scrollable-header,
.p-datatable .p-datatable-scrollable-footer {
    background: #161d21;
  }
  .p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-datatable-table > .p-datatable-thead,
.p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-datatable-table > .p-datatable-tfoot, .p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-virtualscroller > .p-datatable-table > .p-datatable-thead,
.p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-virtualscroller > .p-datatable-table > .p-datatable-tfoot {
    background-color: #161d21;
  }
  .p-datatable .p-datatable-loading-icon {
    font-size: 2rem;
  }
  .p-datatable .p-datatable-loading-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-header {
    border-width: 1px 1px 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-footer {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-paginator-top {
    border-width: 0 1px 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-paginator-bottom {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th {
    border-width: 1px 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th:last-child {
    border-width: 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td {
    border-width: 1px 0 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td:last-child {
    border-width: 1px 1px 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td {
    border-width: 1px 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td {
    border-width: 1px 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td:last-child {
    border-width: 1px 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td {
    border-width: 0 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td:last-child {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td {
    border-width: 0 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td:last-child {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td {
    border-width: 0 0 0 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 0 1px 0 1px;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd {
    background: #1b2327;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd.p-highlight .p-row-toggler {
    color: #9eade6;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd.p-highlight .p-row-toggler:hover {
    color: #9eade6;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd + .p-row-expanded {
    background: #1b2327;
  }
  .p-datatable.p-datatable-sm .p-datatable-header {
    padding: 0.5rem 0.5rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
    padding: 0.5rem 0.5rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
    padding: 0.5rem 0.5rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-tfoot > tr > td {
    padding: 0.5rem 0.5rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-footer {
    padding: 0.5rem 0.5rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-header {
    padding: 1.25rem 1.25rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-thead > tr > th {
    padding: 1.25rem 1.25rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-tbody > tr > td {
    padding: 1.25rem 1.25rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-tfoot > tr > td {
    padding: 1.25rem 1.25rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-footer {
    padding: 1.25rem 1.25rem;
  }

  .p-datatable-drag-selection-helper {
    background: rgba(158, 173, 230, 0.16);
  }

  .p-dataview .p-paginator-top {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-dataview .p-paginator-bottom {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-dataview .p-dataview-header {
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
  }
  .p-dataview .p-dataview-content {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 0 none;
    padding: 0;
  }
  .p-dataview.p-dataview-list .p-dataview-content > .p-grid > div {
    border: solid #263238;
    border-width: 0 0 1px 0;
  }
  .p-dataview .p-dataview-footer {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-dataview .p-dataview-loading-icon {
    font-size: 2rem;
  }
  .p-dataview .p-dataview-loading-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }

  .p-datascroller .p-paginator-top {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-datascroller .p-paginator-bottom {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-datascroller .p-datascroller-header {
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
  }
  .p-datascroller .p-datascroller-content {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 0 none;
    padding: 0;
  }
  .p-datascroller.p-datascroller-inline .p-datascroller-list > li {
    border: solid #263238;
    border-width: 0 0 1px 0;
  }
  .p-datascroller .p-datascroller-footer {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .p-column-filter-row .p-column-filter-menu-button,
.p-column-filter-row .p-column-filter-clear-button {
    margin-left: 0.5rem;
  }

  .p-column-filter-menu-button {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-column-filter-menu-button:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-column-filter-menu-button.p-column-filter-menu-button-open, .p-column-filter-menu-button.p-column-filter-menu-button-open:hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-column-filter-menu-button.p-column-filter-menu-button-active, .p-column-filter-menu-button.p-column-filter-menu-button-active:hover {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-column-filter-menu-button:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }

  .p-column-filter-clear-button {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-column-filter-clear-button:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-column-filter-clear-button:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }

  .p-column-filter-overlay {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 1px solid #263238;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    min-width: 12.5rem;
  }
  .p-column-filter-overlay .p-column-filter-row-items {
    padding: 0.5rem 0.5rem;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item {
    margin: 0 0 4px 0;
    padding: 0.5rem 1rem;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item:not(.p-highlight):not(.p-disabled):hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }

  .p-column-filter-overlay-menu .p-column-filter-operator {
    padding: 0.5rem 1.5rem;
    border-bottom: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint {
    padding: 1rem;
    border-bottom: 1px solid #263238;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint .p-column-filter-matchmode-dropdown {
    margin-bottom: 0.5rem;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint .p-column-filter-remove-button {
    margin-top: 0.5rem;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint:last-child {
    border-bottom: 0 none;
  }
  .p-column-filter-overlay-menu .p-column-filter-add-rule {
    padding: 0.5rem 1rem;
  }
  .p-column-filter-overlay-menu .p-column-filter-buttonbar {
    padding: 1rem;
  }

  .p-orderlist .p-orderlist-controls {
    padding: 1rem;
  }
  .p-orderlist .p-orderlist-controls .p-button {
    margin-bottom: 0.5rem;
  }
  .p-orderlist .p-orderlist-header {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    padding: 1rem;
    font-weight: 600;
    border-bottom: 0 none;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-orderlist .p-orderlist-filter-container {
    padding: 1rem;
    background: #161d21;
    border: 2px solid #263238;
    border-bottom: 0 none;
  }
  .p-orderlist .p-orderlist-filter-container .p-orderlist-filter-input {
    padding-right: 1.75rem;
  }
  .p-orderlist .p-orderlist-filter-container .p-orderlist-filter-icon {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-orderlist .p-orderlist-list {
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    padding: 0.5rem 0.5rem;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item {
    padding: 0.5rem 1rem;
    margin: 0 0 4px 0;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: transform 0.3s, box-shadow 0.3s;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }
  .p-orderlist.p-orderlist-striped .p-orderlist-list .p-orderlist-item:nth-child(even) {
    background: rgba(255, 255, 255, 0.01);
  }
  .p-orderlist.p-orderlist-striped .p-orderlist-list .p-orderlist-item:nth-child(even):hover {
    background: rgba(158, 173, 230, 0.08);
  }

  .p-organizationchart .p-organizationchart-node-content.p-organizationchart-selectable-node:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-organizationchart .p-organizationchart-node-content.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-organizationchart .p-organizationchart-node-content.p-highlight .p-node-toggler i {
    color: rgba(57, 87, 204, 0.16);
  }
  .p-organizationchart .p-organizationchart-line-down {
    background: #263238;
  }
  .p-organizationchart .p-organizationchart-line-left {
    border-right: 2px solid #263238;
    border-color: #263238;
  }
  .p-organizationchart .p-organizationchart-line-top {
    border-top: 2px solid #263238;
    border-color: #263238;
  }
  .p-organizationchart .p-organizationchart-node-content {
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    padding: 1rem;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler {
    background: inherit;
    color: inherit;
    border-radius: 50%;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }

  .p-paginator {
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    border: solid #263238;
    border-width: 2px;
    padding: 0.5rem 1rem;
    border-radius: 6px;
  }
  .p-paginator .p-paginator-first,
.p-paginator .p-paginator-prev,
.p-paginator .p-paginator-next,
.p-paginator .p-paginator-last {
    background-color: transparent;
    border: 0 none;
    color: rgba(255, 255, 255, 0.6);
    min-width: 2.857rem;
    height: 2.857rem;
    margin: 0.143rem;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    border-color: transparent;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-paginator .p-paginator-first {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-paginator .p-paginator-last {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-paginator .p-dropdown {
    margin-left: 0.5rem;
    height: 2.857rem;
  }
  .p-paginator .p-dropdown .p-dropdown-label {
    padding-right: 0;
  }
  .p-paginator .p-paginator-page-input {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
  .p-paginator .p-paginator-page-input .p-inputtext {
    max-width: 2.857rem;
  }
  .p-paginator .p-paginator-current {
    background-color: transparent;
    border: 0 none;
    color: rgba(255, 255, 255, 0.6);
    min-width: 2.857rem;
    height: 2.857rem;
    margin: 0.143rem;
    padding: 0 0.5rem;
  }
  .p-paginator .p-paginator-pages .p-paginator-page {
    background-color: transparent;
    border: 0 none;
    color: rgba(255, 255, 255, 0.6);
    min-width: 2.857rem;
    height: 2.857rem;
    margin: 0.143rem;
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    border-color: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    border-color: transparent;
    color: rgba(255, 255, 255, 0.87);
  }

  .p-picklist .p-picklist-buttons {
    padding: 1rem;
  }
  .p-picklist .p-picklist-buttons .p-button {
    margin-bottom: 0.5rem;
  }
  .p-picklist .p-picklist-header {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    padding: 1rem;
    font-weight: 600;
    border-bottom: 0 none;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-picklist .p-picklist-filter-container {
    padding: 1rem;
    background: #161d21;
    border: 2px solid #263238;
    border-bottom: 0 none;
  }
  .p-picklist .p-picklist-filter-container .p-picklist-filter-input {
    padding-right: 1.75rem;
  }
  .p-picklist .p-picklist-filter-container .p-picklist-filter-icon {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-picklist .p-picklist-list {
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    padding: 0.5rem 0.5rem;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-picklist .p-picklist-list .p-picklist-item {
    padding: 0.5rem 1rem;
    margin: 0 0 4px 0;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    background: transparent;
    transition: transform 0.3s, box-shadow 0.3s;
  }
  .p-picklist .p-picklist-list .p-picklist-item:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-picklist .p-picklist-list .p-picklist-item:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-picklist .p-picklist-list .p-picklist-item.p-highlight {
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
  }

  .p-tree {
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    padding: 1rem;
    border-radius: 6px;
  }
  .p-tree .p-tree-container .p-treenode {
    padding: 0.143rem;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content {
    border-radius: 6px;
    transition: box-shadow 0.3s;
    padding: 0.5rem;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler {
    margin-right: 0.5rem;
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-treenode-icon {
    margin-right: 0.5rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox .p-indeterminate .p-checkbox-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-tree-toggler,
.p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-treenode-icon {
    color: #9eade6;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-tree-toggler:hover,
.p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-treenode-icon:hover {
    color: #9eade6;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-treenode-selectable:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-treenode-dragover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tree .p-tree-filter-container {
    margin-bottom: 0.5rem;
  }
  .p-tree .p-tree-filter-container .p-tree-filter {
    width: 100%;
    padding-right: 1.75rem;
  }
  .p-tree .p-tree-filter-container .p-tree-filter-icon {
    right: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-tree .p-treenode-children {
    padding: 0 0 0 1rem;
  }
  .p-tree .p-tree-loading-icon {
    font-size: 2rem;
  }
  .p-tree .p-tree-loading-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-tree .p-treenode-droppoint {
    height: 0.5rem;
  }
  .p-tree .p-treenode-droppoint.p-treenode-droppoint-active {
    background: rgba(96, 121, 214, 0.16);
  }

  .p-treetable .p-paginator-top {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-treetable .p-paginator-bottom {
    border-width: 0 0 2px 0;
    border-radius: 0;
  }
  .p-treetable .p-treetable-header {
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
  }
  .p-treetable .p-treetable-footer {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
    font-weight: 600;
  }
  .p-treetable .p-treetable-thead > tr > th {
    text-align: left;
    padding: 1rem 1rem;
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    transition: box-shadow 0.3s;
  }
  .p-treetable .p-treetable-tfoot > tr > td {
    text-align: left;
    padding: 1rem 1rem;
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
  }
  .p-treetable .p-sortable-column {
    outline-color: #9eade6;
  }
  .p-treetable .p-sortable-column .p-sortable-column-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-left: 0.5rem;
  }
  .p-treetable .p-sortable-column .p-sortable-column-badge {
    border-radius: 50%;
    height: 1.143rem;
    min-width: 1.143rem;
    line-height: 1.143rem;
    color: #9eade6;
    background: rgba(158, 173, 230, 0.16);
    margin-left: 0.5rem;
  }
  .p-treetable .p-sortable-column:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-treetable .p-sortable-column:not(.p-highlight):hover .p-sortable-column-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-treetable .p-sortable-column.p-highlight {
    background: #161d21;
    color: #9eade6;
  }
  .p-treetable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #9eade6;
  }
  .p-treetable .p-treetable-tbody > tr {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    transition: box-shadow 0.3s;
  }
  .p-treetable .p-treetable-tbody > tr > td {
    text-align: left;
    border: 2px solid #263238;
    border-width: 0 0 2px 0;
    padding: 1rem 1rem;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    margin-right: 0.5rem;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler + .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler + .p-checkbox .p-indeterminate .p-checkbox-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-treetable .p-treetable-tbody > tr:focus-visible {
    outline: 0.15rem solid #9eade6;
    outline-offset: -0.15rem;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight .p-treetable-toggler {
    color: #9eade6;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight .p-treetable-toggler:hover {
    color: #9eade6;
  }
  .p-treetable.p-treetable-selectable .p-treetable-tbody > tr:not(.p-highlight):hover, .p-treetable.p-treetable-hoverable-rows .p-treetable-tbody > tr:not(.p-highlight):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-treetable.p-treetable-selectable .p-treetable-tbody > tr:not(.p-highlight):hover .p-treetable-toggler, .p-treetable.p-treetable-hoverable-rows .p-treetable-tbody > tr:not(.p-highlight):hover .p-treetable-toggler {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-treetable .p-column-resizer-helper {
    background: #9eade6;
  }
  .p-treetable .p-treetable-scrollable-header,
.p-treetable .p-treetable-scrollable-footer {
    background: #161d21;
  }
  .p-treetable .p-treetable-loading-icon {
    font-size: 2rem;
  }
  .p-treetable .p-treetable-loading-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-header {
    border-width: 1px 1px 0 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-footer {
    border-width: 0 1px 1px 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-top {
    border-width: 0 1px 0 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-bottom {
    border-width: 0 1px 1px 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-thead > tr > th {
    border-width: 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-tbody > tr > td {
    border-width: 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-tfoot > tr > td {
    border-width: 1px;
  }
  .p-treetable.p-treetable-striped .p-treetable-tbody > tr.p-row-odd {
    background: #1b2327;
  }
  .p-treetable.p-treetable-striped .p-treetable-tbody > tr.p-row-odd.p-highlight {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-treetable.p-treetable-striped .p-treetable-tbody > tr.p-row-odd.p-highlight .p-row-toggler {
    color: #9eade6;
  }
  .p-treetable.p-treetable-striped .p-treetable-tbody > tr.p-row-odd.p-highlight .p-row-toggler:hover {
    color: #9eade6;
  }
  .p-treetable.p-treetable-striped .p-treetable-tbody > tr.p-row-odd + .p-row-expanded {
    background: #1b2327;
  }
  .p-treetable.p-treetable-sm .p-treetable-header {
    padding: 0.875rem 0.875rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-thead > tr > th {
    padding: 0.5rem 0.5rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-tbody > tr > td {
    padding: 0.5rem 0.5rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-tfoot > tr > td {
    padding: 0.5rem 0.5rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-footer {
    padding: 0.5rem 0.5rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-header {
    padding: 1.25rem 1.25rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-thead > tr > th {
    padding: 1.25rem 1.25rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-tbody > tr > td {
    padding: 1.25rem 1.25rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-tfoot > tr > td {
    padding: 1.25rem 1.25rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-footer {
    padding: 1.25rem 1.25rem;
  }

  .p-timeline .p-timeline-event-marker {
    border: 2px solid #9eade6;
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    background-color: #161d21;
  }
  .p-timeline .p-timeline-event-connector {
    background-color: #263238;
  }
  .p-timeline.p-timeline-vertical .p-timeline-event-opposite,
.p-timeline.p-timeline-vertical .p-timeline-event-content {
    padding: 0 1rem;
  }
  .p-timeline.p-timeline-vertical .p-timeline-event-connector {
    width: 2px;
  }
  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
.p-timeline.p-timeline-horizontal .p-timeline-event-content {
    padding: 1rem 0;
  }
  .p-timeline.p-timeline-horizontal .p-timeline-event-connector {
    height: 2px;
  }

  .p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 1rem;
    border: 2px solid #263238;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
    border-radius: 6px;
    transition: box-shadow 0.3s;
  }
  .p-accordion .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
    margin-right: 0.5rem;
  }
  .p-accordion .p-accordion-header:not(.p-disabled) .p-accordion-header-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-accordion .p-accordion-header:not(.p-highlight):not(.p-disabled):hover .p-accordion-header-link {
    background: rgba(158, 173, 230, 0.08);
    border-color: #263238;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-accordion .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link {
    background: #161d21;
    border-color: #263238;
    color: rgba(255, 255, 255, 0.87);
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .p-accordion .p-accordion-header:not(.p-disabled).p-highlight:hover .p-accordion-header-link {
    border-color: #263238;
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-accordion .p-accordion-content {
    padding: 1rem;
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-accordion .p-accordion-tab {
    margin-bottom: 0;
  }
  .p-accordion .p-accordion-tab .p-accordion-header .p-accordion-header-link {
    border-radius: 0;
  }
  .p-accordion .p-accordion-tab .p-accordion-content {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .p-accordion .p-accordion-tab:not(:first-child) .p-accordion-header .p-accordion-header-link {
    border-top: 0 none;
  }
  .p-accordion .p-accordion-tab:not(:first-child) .p-accordion-header:not(.p-highlight):not(.p-disabled):hover .p-accordion-header-link, .p-accordion .p-accordion-tab:not(:first-child) .p-accordion-header:not(.p-disabled).p-highlight:hover .p-accordion-header-link {
    border-top: 0 none;
  }
  .p-accordion .p-accordion-tab:first-child .p-accordion-header .p-accordion-header-link {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-accordion .p-accordion-tab:last-child .p-accordion-header:not(.p-highlight) .p-accordion-header-link {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-accordion .p-accordion-tab:last-child .p-accordion-header:not(.p-highlight) .p-accordion-content {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-card {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }
  .p-card .p-card-body {
    padding: 1rem;
  }
  .p-card .p-card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  .p-card .p-card-subtitle {
    font-weight: 400;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-card .p-card-content {
    padding: 1rem 0;
  }
  .p-card .p-card-footer {
    padding: 1rem 0 0 0;
  }

  .p-fieldset {
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
  }
  .p-fieldset .p-fieldset-legend {
    padding: 1rem;
    border: 2px solid #263238;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
    border-radius: 6px;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend {
    padding: 0;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a {
    padding: 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a .p-fieldset-toggler {
    margin-right: 0.5rem;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend:hover {
    background: rgba(158, 173, 230, 0.08);
    border-color: #263238;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-fieldset .p-fieldset-content {
    padding: 1rem;
  }

  .p-divider .p-divider-content {
    background-color: #161d21;
  }
  .p-divider.p-divider-horizontal {
    margin: 1rem 0;
    padding: 0 1rem;
  }
  .p-divider.p-divider-horizontal:before {
    border-top: 1px #263238;
  }
  .p-divider.p-divider-horizontal .p-divider-content {
    padding: 0 0.5rem;
  }
  .p-divider.p-divider-vertical {
    margin: 0 1rem;
    padding: 1rem 0;
  }
  .p-divider.p-divider-vertical:before {
    border-left: 1px #263238;
  }
  .p-divider.p-divider-vertical .p-divider-content {
    padding: 0.5rem 0;
  }

  .p-panel .p-panel-header {
    border: 2px solid #263238;
    padding: 1rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-panel .p-panel-header .p-panel-title {
    font-weight: 600;
  }
  .p-panel .p-panel-header .p-panel-header-icon {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-panel .p-panel-header .p-panel-header-icon:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-panel .p-panel-header .p-panel-header-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-panel.p-panel-toggleable .p-panel-header {
    padding: 0.5rem 1rem;
  }
  .p-panel .p-panel-content {
    padding: 1rem;
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
    border-top: 0 none;
  }
  .p-panel .p-panel-footer {
    padding: 0.5rem 1rem;
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border-top: 0 none;
  }

  .p-splitter {
    border: 2px solid #263238;
    background: #161d21;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-splitter .p-splitter-gutter {
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    background: rgba(255, 255, 255, 0.03);
  }
  .p-splitter .p-splitter-gutter .p-splitter-gutter-handle {
    background: #263238;
  }
  .p-splitter .p-splitter-gutter-resizing {
    background: #263238;
  }

  .p-scrollpanel .p-scrollpanel-bar {
    background: #263238;
    border: 0 none;
  }

  .p-tabview .p-tabview-nav {
    background: transparent;
    border: 1px solid #263238;
    border-width: 0 0 2px 0;
  }
  .p-tabview .p-tabview-nav li {
    margin-right: 0;
  }
  .p-tabview .p-tabview-nav li .p-tabview-nav-link {
    border: solid #263238;
    border-width: 0 0 2px 0;
    border-color: transparent transparent #263238 transparent;
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    padding: 1rem;
    font-weight: 600;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    transition: box-shadow 0.3s;
    margin: 0 0 -2px 0;
  }
  .p-tabview .p-tabview-nav li .p-tabview-nav-link:not(.p-disabled):focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 1px #9eade6;
  }
  .p-tabview .p-tabview-nav li:not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
    background: #161d21;
    border-color: #9eade6;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    background: #161d21;
    border-color: #9eade6;
    color: #9eade6;
  }
  .p-tabview .p-tabview-close {
    margin-left: 0.5rem;
  }
  .p-tabview .p-tabview-nav-btn.p-link {
    background: #161d21;
    color: #9eade6;
    width: 2.857rem;
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
    border-radius: 0;
  }
  .p-tabview .p-tabview-nav-btn.p-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 1px #9eade6;
  }
  .p-tabview .p-tabview-panels {
    background: #161d21;
    padding: 1rem;
    border: 0 none;
    color: rgba(255, 255, 255, 0.87);
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-toolbar {
    background: #161d21;
    border: 2px solid #263238;
    padding: 1rem;
    border-radius: 6px;
    gap: 0.5rem;
  }
  .p-toolbar .p-toolbar-separator {
    margin: 0 0.5rem;
  }

  .p-confirm-popup {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
    box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
  }
  .p-confirm-popup .p-confirm-popup-content {
    padding: 1rem;
  }
  .p-confirm-popup .p-confirm-popup-footer {
    text-align: right;
    padding: 0 1rem 1rem 1rem;
  }
  .p-confirm-popup .p-confirm-popup-footer button {
    margin: 0 0.5rem 0 0;
    width: auto;
  }
  .p-confirm-popup .p-confirm-popup-footer button:last-child {
    margin: 0;
  }
  .p-confirm-popup:after {
    border: solid transparent;
    border-color: rgba(22, 29, 33, 0);
    border-bottom-color: #161d21;
  }
  .p-confirm-popup:before {
    border: solid transparent;
    border-color: rgba(38, 50, 56, 0);
    border-bottom-color: #243035;
  }
  .p-confirm-popup.p-confirm-popup-flipped:after {
    border-top-color: #161d21;
  }
  .p-confirm-popup.p-confirm-popup-flipped:before {
    border-top-color: #263238;
  }
  .p-confirm-popup .p-confirm-popup-icon {
    font-size: 1.5rem;
  }
  .p-confirm-popup .p-confirm-popup-icon.p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
  .p-confirm-popup .p-confirm-popup-message {
    margin-left: 1rem;
  }

  .p-dialog {
    border-radius: 6px;
    box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
    border: 2px solid #263238;
  }
  .p-dialog .p-dialog-header {
    border-bottom: 0 none;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    padding: 1.5rem;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-dialog .p-dialog-header .p-dialog-title {
    font-weight: 600;
    font-size: 1.25rem;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    margin-right: 0.5rem;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:last-child {
    margin-right: 0;
  }
  .p-dialog .p-dialog-content {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    padding: 0 1.5rem 2rem 1.5rem;
  }
  .p-dialog .p-dialog-content:last-of-type {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-dialog .p-dialog-footer {
    border-top: 0 none;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    padding: 0 1.5rem 1.5rem 1.5rem;
    text-align: right;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-dialog .p-dialog-footer button {
    margin: 0 0.5rem 0 0;
    width: auto;
  }
  .p-dialog.p-dialog-maximized .p-dialog-header, .p-dialog.p-dialog-maximized .p-dialog-content:last-of-type {
    border-radius: 0;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-icon {
    font-size: 2rem;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-message {
    margin-left: 1rem;
  }

  .p-overlaypanel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
    box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
  }
  .p-overlaypanel .p-overlaypanel-content {
    padding: 1rem;
  }
  .p-overlaypanel .p-overlaypanel-close {
    background: #9eade6;
    color: #121212;
    width: 2rem;
    height: 2rem;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    border-radius: 50%;
    position: absolute;
    top: -1rem;
    right: -1rem;
  }
  .p-overlaypanel .p-overlaypanel-close:enabled:hover {
    background: #8fa0e2;
    color: #121212;
  }
  .p-overlaypanel:after {
    border: solid transparent;
    border-color: rgba(22, 29, 33, 0);
    border-bottom-color: #161d21;
  }
  .p-overlaypanel:before {
    border: solid transparent;
    border-color: rgba(38, 50, 56, 0);
    border-bottom-color: #243035;
  }
  .p-overlaypanel.p-overlaypanel-flipped:after {
    border-top-color: #161d21;
  }
  .p-overlaypanel.p-overlaypanel-flipped:before {
    border-top-color: #263238;
  }

  .p-sidebar {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
  }
  .p-sidebar .p-sidebar-header {
    padding: 1rem;
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close,
.p-sidebar .p-sidebar-header .p-sidebar-icon {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close:enabled:hover,
.p-sidebar .p-sidebar-header .p-sidebar-icon:enabled:hover {
    color: rgba(255, 255, 255, 0.87);
    border-color: transparent;
    background: rgba(158, 173, 230, 0.08);
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close:focus-visible,
.p-sidebar .p-sidebar-header .p-sidebar-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-sidebar .p-sidebar-header + .p-sidebar-content {
    padding-top: 0;
  }
  .p-sidebar .p-sidebar-content {
    padding: 1rem;
  }

  .p-tooltip .p-tooltip-text {
    background: #263238;
    color: rgba(255, 255, 255, 0.87);
    padding: 0.5rem 0.75rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }
  .p-tooltip.p-tooltip-right .p-tooltip-arrow {
    border-right-color: #263238;
  }
  .p-tooltip.p-tooltip-left .p-tooltip-arrow {
    border-left-color: #263238;
  }
  .p-tooltip.p-tooltip-top .p-tooltip-arrow {
    border-top-color: #263238;
  }
  .p-tooltip.p-tooltip-bottom .p-tooltip-arrow {
    border-bottom-color: #263238;
  }

  .p-fileupload .p-fileupload-buttonbar {
    background: #161d21;
    padding: 1rem;
    border: 2px solid #263238;
    color: rgba(255, 255, 255, 0.87);
    border-bottom: 0 none;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    gap: 0.5rem;
  }
  .p-fileupload .p-fileupload-buttonbar .p-button {
    margin-right: 0.5rem;
  }
  .p-fileupload .p-fileupload-content {
    background: #161d21;
    padding: 2rem 1rem;
    border: 2px solid #263238;
    color: rgba(255, 255, 255, 0.87);
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-fileupload .p-progressbar {
    height: 0.25rem;
  }
  .p-fileupload .p-fileupload-row > div {
    padding: 1rem 1rem;
  }
  .p-fileupload.p-fileupload-advanced .p-message {
    margin-top: 0;
  }

  .p-breadcrumb {
    background: #161d21;
    border: 2px solid #263238;
    border-radius: 6px;
    padding: 1rem;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link {
    transition: box-shadow 0.3s;
    border-radius: 6px;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-breadcrumb .p-breadcrumb-list li.p-menuitem-separator {
    margin: 0 0.5rem 0 0.5rem;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
  }

  .p-contextmenu {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-contextmenu .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-contextmenu .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-contextmenu .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-contextmenu .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-contextmenu .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-contextmenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-contextmenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-contextmenu .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-contextmenu .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-contextmenu .p-submenu-list {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }
  .p-contextmenu .p-menuitem.p-menuitem-active > .p-menuitem-link {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-contextmenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-contextmenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-icon, .p-contextmenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-contextmenu .p-menu-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }
  .p-contextmenu .p-submenu-icon {
    font-size: 0.875rem;
    transition: transform 0.3s;
  }
  .p-contextmenu .p-submenu-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .p-dock .p-dock-list {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.5rem 0.5rem;
    border-radius: 0.5rem;
  }
  .p-dock .p-dock-item {
    padding: 0.5rem;
  }
  .p-dock .p-dock-action {
    width: 4rem;
    height: 4rem;
  }
  .p-dock.p-dock-magnification.p-dock-top .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-top .p-dock-item-second-next, .p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-second-next {
    margin: 0 0.9rem;
  }
  .p-dock.p-dock-magnification.p-dock-top .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-top .p-dock-item-next, .p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-next {
    margin: 0 1.3rem;
  }
  .p-dock.p-dock-magnification.p-dock-top .p-dock-item-current, .p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-current {
    margin: 0 1.5rem;
  }
  .p-dock.p-dock-magnification.p-dock-left .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-left .p-dock-item-second-next, .p-dock.p-dock-magnification.p-dock-right .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-right .p-dock-item-second-next {
    margin: 0.9rem 0;
  }
  .p-dock.p-dock-magnification.p-dock-left .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-left .p-dock-item-next, .p-dock.p-dock-magnification.p-dock-right .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-right .p-dock-item-next {
    margin: 1.3rem 0;
  }
  .p-dock.p-dock-magnification.p-dock-left .p-dock-item-current, .p-dock.p-dock-magnification.p-dock-right .p-dock-item-current {
    margin: 1.5rem 0;
  }

  @media screen and (max-width: 960px) {
    .p-dock.p-dock-top .p-dock-container, .p-dock.p-dock-bottom .p-dock-container {
      overflow-x: auto;
      width: 100%;
    }
    .p-dock.p-dock-top .p-dock-container .p-dock-list, .p-dock.p-dock-bottom .p-dock-container .p-dock-list {
      margin: 0 auto;
    }
    .p-dock.p-dock-left .p-dock-container, .p-dock.p-dock-right .p-dock-container {
      overflow-y: auto;
      height: 100%;
    }
    .p-dock.p-dock-left .p-dock-container .p-dock-list, .p-dock.p-dock-right .p-dock-container .p-dock-list {
      margin: auto 0;
    }
    .p-dock.p-dock-magnification.p-dock-top .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-top .p-dock-item-second-next,
.p-dock.p-dock-magnification.p-dock-top .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-top .p-dock-item-next,
.p-dock.p-dock-magnification.p-dock-top .p-dock-item-current, .p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-second-next,
.p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-next,
.p-dock.p-dock-magnification.p-dock-bottom .p-dock-item-current, .p-dock.p-dock-magnification.p-dock-left .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-left .p-dock-item-second-next,
.p-dock.p-dock-magnification.p-dock-left .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-left .p-dock-item-next,
.p-dock.p-dock-magnification.p-dock-left .p-dock-item-current, .p-dock.p-dock-magnification.p-dock-right .p-dock-item-second-prev,
.p-dock.p-dock-magnification.p-dock-right .p-dock-item-second-next,
.p-dock.p-dock-magnification.p-dock-right .p-dock-item-prev,
.p-dock.p-dock-magnification.p-dock-right .p-dock-item-next,
.p-dock.p-dock-magnification.p-dock-right .p-dock-item-current {
      transform: none;
      margin: 0;
    }
  }
  .p-megamenu {
    padding: 0.5rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-left: 0.5rem;
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link > .p-submenu-icon {
    margin-left: auto;
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link,
.p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-text,
.p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-icon,
.p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link .p-submenu-icon,
.p-megamenu .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-megamenu .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-megamenu .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-megamenu .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-megamenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-megamenu .p-megamenu-panel {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-megamenu .p-megamenu-submenu-header {
    margin: 0;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-megamenu .p-megamenu-submenu {
    padding: 0.5rem 0.5rem;
    width: 12.5rem;
  }
  .p-megamenu .p-megamenu-submenu .p-menu-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }
  .p-megamenu .p-menuitem.p-menuitem-active > .p-menuitem-link {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-megamenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-icon, .p-megamenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu.p-megamenu-vertical {
    width: 12.5rem;
    padding: 0.5rem 0.5rem;
  }
  .p-megamenu .p-megamenu-button {
    width: 2rem;
    height: 2rem;
    color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-megamenu .p-megamenu-button:hover {
    color: rgba(255, 255, 255, 0.87);
    background: rgba(158, 173, 230, 0.08);
  }
  .p-megamenu .p-megamenu-button:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-megamenu .p-submenu-icon {
    transition: transform 0.3s;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list .p-menu-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list .p-submenu-icon {
    font-size: 0.875rem;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list .p-submenu-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem {
    width: 100%;
    position: static;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem > .p-menuitem-link > .p-submenu-icon {
    margin-left: auto;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link > .p-submenu-icon {
    transform: rotate(-180deg);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list .p-submenu-list {
    width: 100%;
    position: static;
    box-shadow: none;
    border: 0 none;
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list .p-submenu-list .p-submenu-icon {
    transition: transform 0.3s;
    transform: rotate(90deg);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list .p-submenu-list .p-menuitem-active > .p-menuitem-link > .p-submenu-icon {
    transform: rotate(-90deg);
  }
  .p-megamenu.p-megamenu-mobile-active .p-megamenu-root-list .p-menuitem {
    width: 100%;
    position: static;
  }

  .p-menu {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-menu .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-menu .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menu .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-menu .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-menu .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-menu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menu .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menu .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-menu.p-menu-overlay {
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-menu .p-submenu-header {
    margin: 0;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
  }
  .p-menu .p-menu-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }

  .p-menubar {
    padding: 0.5rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
  }
  .p-menubar .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-menubar .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-menubar .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-menubar .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-menubar .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-left: 0.5rem;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link,
.p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-text,
.p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link .p-submenu-icon,
.p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-submenu-list {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    width: 12.5rem;
  }
  .p-menubar .p-submenu-list .p-menu-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }
  .p-menubar .p-submenu-list .p-submenu-icon {
    font-size: 0.875rem;
  }
  .p-menubar .p-submenu-list .p-submenu-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
  .p-menubar .p-menuitem.p-menuitem-active > .p-menuitem-link {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-menubar .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-menubar .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-icon, .p-menubar .p-menuitem.p-menuitem-active > .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }

  @media screen and (max-width: 960px) {
    .p-menubar {
      position: relative;
    }
    .p-menubar .p-menubar-button {
      display: flex;
      width: 2rem;
      height: 2rem;
      color: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    }
    .p-menubar .p-menubar-button:hover {
      color: rgba(255, 255, 255, 0.87);
      background: rgba(158, 173, 230, 0.08);
    }
    .p-menubar .p-menubar-button:focus {
      outline: 0 none;
      outline-offset: 0;
      box-shadow: 0 0 0 1px #9eade6;
    }
    .p-menubar .p-menubar-root-list {
      position: absolute;
      display: none;
      padding: 0.5rem 0.5rem;
      background: #161d21;
      border: 2px solid #263238;
      box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
      width: 100%;
    }
    .p-menubar .p-menubar-root-list .p-menu-separator {
      border-top: 1px solid #263238;
      margin: 4px 0;
    }
    .p-menubar .p-menubar-root-list .p-submenu-icon {
      font-size: 0.875rem;
    }
    .p-menubar .p-menubar-root-list .p-submenu-icon.p-icon {
      width: 0.875rem;
      height: 0.875rem;
    }
    .p-menubar .p-menubar-root-list > .p-menuitem {
      width: 100%;
      position: static;
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link {
      padding: 0.75rem 1rem;
      color: rgba(255, 255, 255, 0.87);
      border-radius: 6px;
      transition: box-shadow 0.3s;
      user-select: none;
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-text {
      color: rgba(255, 255, 255, 0.87);
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-icon {
      color: rgba(255, 255, 255, 0.6);
      margin-right: 0.5rem;
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link .p-submenu-icon {
      color: rgba(255, 255, 255, 0.6);
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover {
      background: rgba(158, 173, 230, 0.08);
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
      color: rgba(255, 255, 255, 0.87);
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
      color: rgba(255, 255, 255, 0.87);
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
      color: rgba(255, 255, 255, 0.87);
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link:focus {
      outline: 0 none;
      outline-offset: 0;
      box-shadow: inset 0 0 0 0.15rem #9eade6;
    }
    .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-link > .p-submenu-icon {
      margin-left: auto;
      transition: transform 0.3s;
    }
    .p-menubar .p-menubar-root-list > .p-menuitem.p-menuitem-active > .p-menuitem-link > .p-submenu-icon {
      transform: rotate(-180deg);
    }
    .p-menubar .p-menubar-root-list .p-submenu-list {
      width: 100%;
      position: static;
      box-shadow: none;
      border: 0 none;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-submenu-icon {
      transition: transform 0.3s;
      transform: rotate(90deg);
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem-active > .p-menuitem-link > .p-submenu-icon {
      transform: rotate(-90deg);
    }
    .p-menubar .p-menubar-root-list .p-menuitem {
      width: 100%;
      position: static;
    }
    .p-menubar .p-menubar-root-list ul li a {
      padding-left: 2.25rem;
    }
    .p-menubar .p-menubar-root-list ul li ul li a {
      padding-left: 3.75rem;
    }
    .p-menubar .p-menubar-root-list ul li ul li ul li a {
      padding-left: 5.25rem;
    }
    .p-menubar .p-menubar-root-list ul li ul li ul li ul li a {
      padding-left: 6.75rem;
    }
    .p-menubar .p-menubar-root-list ul li ul li ul li ul li ul li a {
      padding-left: 8.25rem;
    }
    .p-menubar.p-menubar-mobile-active .p-menubar-root-list {
      display: flex;
      flex-direction: column;
      top: 100%;
      left: 0;
      z-index: 1;
    }
  }
  .p-panelmenu .p-panelmenu-header > a {
    padding: 1rem;
    border: 2px solid #263238;
    color: rgba(255, 255, 255, 0.87);
    background: #161d21;
    font-weight: 600;
    border-radius: 6px;
    transition: box-shadow 0.3s;
  }
  .p-panelmenu .p-panelmenu-header > a .p-panelmenu-icon {
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-header > a .p-menuitem-icon {
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-header > a:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-highlight):not(.p-disabled) > a:hover {
    background: rgba(158, 173, 230, 0.08);
    border-color: #263238;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-panelmenu .p-panelmenu-header.p-highlight {
    margin-bottom: 0;
  }
  .p-panelmenu .p-panelmenu-header.p-highlight > a {
    background: #161d21;
    border-color: #263238;
    color: rgba(255, 255, 255, 0.87);
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .p-panelmenu .p-panelmenu-header.p-highlight:not(.p-disabled) > a:hover {
    border-color: #263238;
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-panelmenu .p-panelmenu-content {
    padding: 0.5rem 0.5rem;
    border: 2px solid #263238;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-link .p-panelmenu-icon {
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-content .p-submenu-list:not(.p-panelmenu-root-submenu) {
    padding: 0 0 0 1rem;
  }
  .p-panelmenu .p-panelmenu-panel {
    margin-bottom: 0;
  }
  .p-panelmenu .p-panelmenu-panel .p-panelmenu-header > a {
    border-radius: 0;
  }
  .p-panelmenu .p-panelmenu-panel .p-panelmenu-content {
    border-radius: 0;
  }
  .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header > a {
    border-top: 0 none;
  }
  .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header:not(.p-highlight):not(.p-disabled):hover > a, .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header:not(.p-disabled).p-highlight:hover > a {
    border-top: 0 none;
  }
  .p-panelmenu .p-panelmenu-panel:first-child .p-panelmenu-header > a {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-panelmenu .p-panelmenu-panel:last-child .p-panelmenu-header:not(.p-highlight) > a {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-panelmenu .p-panelmenu-panel:last-child .p-panelmenu-content {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-slidemenu {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-slidemenu .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-slidemenu .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-slidemenu .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-slidemenu .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-slidemenu .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-slidemenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-slidemenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-slidemenu .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-slidemenu .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-slidemenu.p-slidemenu-overlay {
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-slidemenu .p-slidemenu-list {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-slidemenu .p-slidemenu.p-slidemenu-active > .p-slidemenu-link {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-slidemenu .p-slidemenu.p-slidemenu-active > .p-slidemenu-link .p-slidemenu-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-slidemenu .p-slidemenu.p-slidemenu-active > .p-slidemenu-link .p-slidemenu-icon, .p-slidemenu .p-slidemenu.p-slidemenu-active > .p-slidemenu-link .p-slidemenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-slidemenu .p-slidemenu-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }
  .p-slidemenu .p-slidemenu-icon {
    font-size: 0.875rem;
  }
  .p-slidemenu .p-slidemenu-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
  .p-slidemenu .p-slidemenu-backward {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
  }

  .p-steps .p-steps-item .p-menuitem-link {
    background: transparent;
    transition: box-shadow 0.3s;
    border-radius: 6px;
    background: transparent;
  }
  .p-steps .p-steps-item .p-menuitem-link .p-steps-number {
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    background: transparent;
    min-width: 2rem;
    height: 2rem;
    line-height: 2rem;
    font-size: 1.143rem;
    z-index: 1;
    border-radius: 50%;
  }
  .p-steps .p-steps-item .p-menuitem-link .p-steps-title {
    margin-top: 0.5rem;
    color: rgba(255, 255, 255, 0.6);
  }
  .p-steps .p-steps-item .p-menuitem-link:not(.p-disabled):focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-number {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-title {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-steps .p-steps-item:before {
    content: " ";
    border-top: 1px solid #263238;
    width: 100%;
    top: 50%;
    left: 0;
    display: block;
    position: absolute;
    margin-top: -1rem;
  }

  .p-tabmenu .p-tabmenu-nav {
    background: transparent;
    border: 1px solid #263238;
    border-width: 0 0 2px 0;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem {
    margin-right: 0;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link {
    border: solid #263238;
    border-width: 0 0 2px 0;
    border-color: transparent transparent #263238 transparent;
    background: #161d21;
    color: rgba(255, 255, 255, 0.6);
    padding: 1rem;
    font-weight: 600;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    transition: box-shadow 0.3s;
    margin: 0 0 -2px 0;
    height: calc(100% + 2px);
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link .p-menuitem-icon {
    margin-right: 0.5rem;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link:not(.p-disabled):focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 1px #9eade6;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem:not(.p-highlight):not(.p-disabled):hover .p-menuitem-link {
    background: #161d21;
    border-color: #9eade6;
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem.p-highlight .p-menuitem-link {
    background: #161d21;
    border-color: #9eade6;
    color: #9eade6;
  }

  .p-tieredmenu {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-tieredmenu .p-menuitem-link {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 6px;
    transition: box-shadow 0.3s;
    user-select: none;
  }
  .p-tieredmenu .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tieredmenu .p-menuitem-link .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 0.5rem;
  }
  .p-tieredmenu .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.6);
  }
  .p-tieredmenu .p-menuitem-link:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-tieredmenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tieredmenu .p-menuitem-link:not(.p-disabled):hover .p-menuitem-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tieredmenu .p-menuitem-link:not(.p-disabled):hover .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tieredmenu .p-menuitem-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 0 0 0.15rem #9eade6;
  }
  .p-tieredmenu.p-tieredmenu-overlay {
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-tieredmenu .p-submenu-list {
    padding: 0.5rem 0.5rem;
    background: #161d21;
    border: 2px solid #263238;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-tieredmenu .p-menuitem.p-menuitem-active > .p-menuitem-link {
    background: rgba(158, 173, 230, 0.08);
  }
  .p-tieredmenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-text {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tieredmenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-menuitem-icon, .p-tieredmenu .p-menuitem.p-menuitem-active > .p-menuitem-link .p-submenu-icon {
    color: rgba(255, 255, 255, 0.87);
  }
  .p-tieredmenu .p-menu-separator {
    border-top: 1px solid #263238;
    margin: 4px 0;
  }
  .p-tieredmenu .p-submenu-icon {
    font-size: 0.875rem;
    transition: transform 0.3s;
  }
  .p-tieredmenu .p-submenu-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .p-inline-message {
    padding: 0.5rem 0.75rem;
    margin: 0;
    border-radius: 6px;
  }
  .p-inline-message.p-inline-message-info {
    background: #a3d7e6;
    border: 4px solid #65bcd6;
    border-width: 2px;
    color: #0e1315;
  }
  .p-inline-message.p-inline-message-info .p-inline-message-icon {
    color: #0e1315;
  }
  .p-inline-message.p-inline-message-success {
    background: #bfd47f;
    border: 4px solid #a2c044;
    border-width: 2px;
    color: #0e1315;
  }
  .p-inline-message.p-inline-message-success .p-inline-message-icon {
    color: #0e1315;
  }
  .p-inline-message.p-inline-message-warn {
    background: #ff9c3e;
    border: 4px solid #ff8817;
    border-width: 2px;
    color: #0e1315;
  }
  .p-inline-message.p-inline-message-warn .p-inline-message-icon {
    color: #0e1315;
  }
  .p-inline-message.p-inline-message-error {
    background: #e6a3b2;
    border: 4px solid #de8499;
    border-width: 2px;
    color: #0e1315;
  }
  .p-inline-message.p-inline-message-error .p-inline-message-icon {
    color: #0e1315;
  }
  .p-inline-message .p-inline-message-icon {
    font-size: 1rem;
    margin-right: 0.5rem;
  }
  .p-inline-message .p-inline-message-icon.p-icon {
    width: 1rem;
    height: 1rem;
  }
  .p-inline-message .p-inline-message-text {
    font-size: 1rem;
  }
  .p-inline-message.p-inline-message-icon-only .p-inline-message-icon {
    margin-right: 0;
  }

  .p-message {
    margin: 1rem 0;
    border-radius: 6px;
  }
  .p-message .p-message-wrapper {
    padding: 1rem 1.5rem;
  }
  .p-message .p-message-close {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: transparent;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-message .p-message-close:hover {
    background: rgba(255, 255, 255, 0.5);
  }
  .p-message .p-message-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-message.p-message-info {
    background: #a3d7e6;
    border: 4px solid #65bcd6;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-message.p-message-info .p-message-icon {
    color: #0e1315;
  }
  .p-message.p-message-info .p-message-close {
    color: #0e1315;
  }
  .p-message.p-message-success {
    background: #bfd47f;
    border: 4px solid #a2c044;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-message.p-message-success .p-message-icon {
    color: #0e1315;
  }
  .p-message.p-message-success .p-message-close {
    color: #0e1315;
  }
  .p-message.p-message-warn {
    background: #ff9c3e;
    border: 4px solid #ff8817;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-message.p-message-warn .p-message-icon {
    color: #0e1315;
  }
  .p-message.p-message-warn .p-message-close {
    color: #0e1315;
  }
  .p-message.p-message-error {
    background: #e6a3b2;
    border: 4px solid #de8499;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-message.p-message-error .p-message-icon {
    color: #0e1315;
  }
  .p-message.p-message-error .p-message-close {
    color: #0e1315;
  }
  .p-message .p-message-text {
    font-size: 1rem;
    font-weight: 400;
  }
  .p-message .p-message-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
  }
  .p-message .p-message-icon.p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
  .p-message .p-message-summary {
    font-weight: 700;
  }
  .p-message .p-message-detail {
    margin-left: 0.5rem;
  }

  .p-toast {
    opacity: 0.9;
  }
  .p-toast .p-toast-message {
    margin: 0 0 1rem 0;
    box-shadow: none;
    border-radius: 6px;
  }
  .p-toast .p-toast-message .p-toast-message-content {
    padding: 1rem;
    border-width: 0 0 0 6px;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-text {
    margin: 0 0 0 1rem;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-icon {
    font-size: 2rem;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-summary {
    font-weight: 600;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-detail {
    margin: 0.5rem 0 0 0;
  }
  .p-toast .p-toast-message .p-toast-icon-close {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: transparent;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-toast .p-toast-message .p-toast-icon-close:hover {
    background: rgba(255, 255, 255, 0.5);
  }
  .p-toast .p-toast-message .p-toast-icon-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-toast .p-toast-message.p-toast-message-info {
    background: #a3d7e6;
    border: 4px solid #65bcd6;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-toast .p-toast-message.p-toast-message-info .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-info .p-toast-icon-close {
    color: #0e1315;
  }
  .p-toast .p-toast-message.p-toast-message-success {
    background: #bfd47f;
    border: 4px solid #a2c044;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-toast .p-toast-message.p-toast-message-success .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-success .p-toast-icon-close {
    color: #0e1315;
  }
  .p-toast .p-toast-message.p-toast-message-warn {
    background: #ff9c3e;
    border: 4px solid #ff8817;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-toast .p-toast-message.p-toast-message-warn .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-warn .p-toast-icon-close {
    color: #0e1315;
  }
  .p-toast .p-toast-message.p-toast-message-error {
    background: #e6a3b2;
    border: 4px solid #de8499;
    border-width: 0 0 0 6px;
    color: #0e1315;
  }
  .p-toast .p-toast-message.p-toast-message-error .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-error .p-toast-icon-close {
    color: #0e1315;
  }

  .p-galleria .p-galleria-close {
    margin: 0.5rem;
    background: transparent;
    color: #f8f9fa;
    width: 4rem;
    height: 4rem;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    border-radius: 50%;
  }
  .p-galleria .p-galleria-close .p-galleria-close-icon {
    font-size: 2rem;
  }
  .p-galleria .p-galleria-close .p-galleria-close-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-galleria .p-galleria-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
  }
  .p-galleria .p-galleria-item-nav {
    background: transparent;
    color: #f8f9fa;
    width: 4rem;
    height: 4rem;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    border-radius: 6px;
    margin: 0 0.5rem;
  }
  .p-galleria .p-galleria-item-nav .p-galleria-item-prev-icon,
.p-galleria .p-galleria-item-nav .p-galleria-item-next-icon {
    font-size: 2rem;
  }
  .p-galleria .p-galleria-item-nav .p-galleria-item-prev-icon.p-icon,
.p-galleria .p-galleria-item-nav .p-galleria-item-next-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-galleria .p-galleria-item-nav:not(.p-disabled):hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
  }
  .p-galleria .p-galleria-caption {
    background: rgba(0, 0, 0, 0.5);
    color: #f8f9fa;
    padding: 1rem;
  }
  .p-galleria .p-galleria-indicators {
    padding: 1rem;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator button {
    background-color: #263238;
    width: 1rem;
    height: 1rem;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    border-radius: 50%;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator button:hover {
    background: #374851;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-galleria.p-galleria-indicators-bottom .p-galleria-indicator, .p-galleria.p-galleria-indicators-top .p-galleria-indicator {
    margin-right: 0.5rem;
  }
  .p-galleria.p-galleria-indicators-left .p-galleria-indicator, .p-galleria.p-galleria-indicators-right .p-galleria-indicator {
    margin-bottom: 0.5rem;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators {
    background: rgba(0, 0, 0, 0.5);
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator button {
    background: rgba(255, 255, 255, 0.4);
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator button:hover {
    background: rgba(255, 255, 255, 0.6);
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background: rgba(158, 173, 230, 0.16);
    color: #9eade6;
  }
  .p-galleria .p-galleria-thumbnail-container {
    background: rgba(0, 0, 0, 0.9);
    padding: 1rem 0.25rem;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-prev,
.p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-next {
    margin: 0.5rem;
    background-color: transparent;
    color: #f8f9fa;
    width: 2rem;
    height: 2rem;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    border-radius: 50%;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-prev:hover,
.p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-next:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-item-content {
    transition: box-shadow 0.3s;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-item-content:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }

  .p-galleria-mask {
    --maskbg: rgba(0, 0, 0, 0.9);
  }

  .p-image-mask {
    --maskbg: rgba(0, 0, 0, 0.9);
  }

  .p-image-preview-indicator {
    background-color: transparent;
    color: #f8f9fa;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-image-preview-indicator .p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .p-image-preview-container:hover > .p-image-preview-indicator {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .p-image-toolbar {
    padding: 1rem;
  }

  .p-image-action.p-link {
    color: #f8f9fa;
    background-color: transparent;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    margin-right: 0.5rem;
  }
  .p-image-action.p-link:last-child {
    margin-right: 0;
  }
  .p-image-action.p-link:hover {
    color: #f8f9fa;
    background-color: rgba(255, 255, 255, 0.1);
  }
  .p-image-action.p-link span {
    font-size: 1.5rem;
  }
  .p-image-action.p-link .p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .p-avatar {
    background-color: #263238;
    border-radius: 6px;
  }
  .p-avatar.p-avatar-lg {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
  }
  .p-avatar.p-avatar-lg .p-avatar-icon {
    font-size: 1.5rem;
  }
  .p-avatar.p-avatar-xl {
    width: 4rem;
    height: 4rem;
    font-size: 2rem;
  }
  .p-avatar.p-avatar-xl .p-avatar-icon {
    font-size: 2rem;
  }

  .p-avatar-circle {
    border-radius: 50%;
  }

  .p-avatar-group .p-avatar {
    border: 2px solid #161d21;
  }

  .p-chip {
    background-color: #263238;
    color: rgba(255, 255, 255, 0.87);
    border-radius: 16px;
    padding: 0 0.75rem;
  }
  .p-chip .p-chip-text {
    line-height: 1.5;
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
  }
  .p-chip .p-chip-icon {
    margin-right: 0.5rem;
  }
  .p-chip img {
    width: 2rem;
    height: 2rem;
    margin-left: -0.75rem;
    margin-right: 0.5rem;
  }
  .p-chip .p-chip-remove-icon {
    border-radius: 6px;
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
    margin-left: 0.5rem;
  }
  .p-chip .p-chip-remove-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }
  .p-chip .p-chip-remove-icon:focus {
    outline: 0 none;
  }

  .p-scrolltop {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
  }
  .p-scrolltop.p-link {
    background: rgba(158, 173, 230, 0.16);
  }
  .p-scrolltop.p-link:hover {
    background: rgba(158, 173, 230, 0.3616);
  }
  .p-scrolltop .p-scrolltop-icon {
    font-size: 1.5rem;
    color: #9eade6;
  }
  .p-scrolltop .p-scrolltop-icon.p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .p-skeleton {
    background-color: rgba(255, 255, 255, 0.06);
    border-radius: 6px;
  }
  .p-skeleton:after {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0));
  }

  .p-tag {
    background: #9eade6;
    color: #121212;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.4rem;
    border-radius: 6px;
  }
  .p-tag.p-tag-success {
    background-color: #cede9c;
    color: #0e1315;
  }
  .p-tag.p-tag-info {
    background-color: #35a4cc;
    color: #ffffff;
  }
  .p-tag.p-tag-warning {
    background-color: #ffe08a;
    color: #0e1315;
  }
  .p-tag.p-tag-danger {
    background-color: #e693a9;
    color: #0e1315;
  }
  .p-tag .p-tag-icon {
    margin-right: 0.25rem;
    font-size: 0.75rem;
  }
  .p-tag .p-tag-icon.p-icon {
    width: 0.75rem;
    height: 0.75rem;
  }

  .p-inplace .p-inplace-display {
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .p-inplace .p-inplace-display:not(.p-disabled):hover {
    background: rgba(158, 173, 230, 0.08);
    color: rgba(255, 255, 255, 0.87);
  }
  .p-inplace .p-inplace-display:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #9eade6;
  }

  .p-progressbar {
    border: 0 none;
    height: 1.5rem;
    background: #263238;
    border-radius: 6px;
  }
  .p-progressbar .p-progressbar-value {
    border: 0 none;
    margin: 0;
    background: #9eade6;
  }
  .p-progressbar .p-progressbar-label {
    color: #121212;
    line-height: 1.5rem;
  }

  .p-terminal {
    background: #161d21;
    color: rgba(255, 255, 255, 0.87);
    border: 2px solid #263238;
    padding: 1rem;
  }
  .p-terminal .p-terminal-input {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
  }

  .p-badge {
    background: #9eade6;
    color: #121212;
    font-size: 0.75rem;
    font-weight: 700;
    min-width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
  }
  .p-badge.p-badge-secondary {
    background-color: #b4bfcd;
    color: #0e1315;
  }
  .p-badge.p-badge-success {
    background-color: #cede9c;
    color: #0e1315;
  }
  .p-badge.p-badge-info {
    background-color: #35a4cc;
    color: #ffffff;
  }
  .p-badge.p-badge-warning {
    background-color: #ffe08a;
    color: #0e1315;
  }
  .p-badge.p-badge-danger {
    background-color: #e693a9;
    color: #0e1315;
  }
  .p-badge.p-badge-lg {
    font-size: 1.125rem;
    min-width: 2.25rem;
    height: 2.25rem;
    line-height: 2.25rem;
  }
  .p-badge.p-badge-xl {
    font-size: 1.5rem;
    min-width: 3rem;
    height: 3rem;
    line-height: 3rem;
  }

  .p-tag {
    background: #9eade6;
    color: #121212;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.4rem;
    border-radius: 6px;
  }
  .p-tag.p-tag-success {
    background-color: #cede9c;
    color: #0e1315;
  }
  .p-tag.p-tag-info {
    background-color: #35a4cc;
    color: #ffffff;
  }
  .p-tag.p-tag-warning {
    background-color: #ffe08a;
    color: #0e1315;
  }
  .p-tag.p-tag-danger {
    background-color: #e693a9;
    color: #0e1315;
  }
}
/* Customizations to the designer theme should be defined here */
/* Customizations to the designer theme should be defined here */
@layer primereact {
  .p-button .p-button-label {
    font-weight: 600;
  }

  .p-buttonset .p-button-label,
.p-togglebutton .p-button-label {
    font-weight: 400;
  }

  .p-carousel .p-carousel-indicators .p-carousel-indicator.p-highlight button {
    background-color: #9eade6;
  }

  .p-galleria .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background-color: #9eade6;
  }

  .p-panel {
    border: 2px solid #263238;
    border-radius: 6px;
  }
  .p-panel .p-panel-header {
    border: 0 none;
  }
  .p-panel .p-panel-content {
    border: 0 none;
  }

  .p-fieldset .p-fieldset-legend {
    border-color: transparent;
  }

  .p-accordion .p-accordion-toggle-icon {
    order: 10;
    margin-left: auto;
  }
  .p-accordion .p-accordion-toggle-icon.pi-chevron-right::before {
    content: "\e90d";
  }
  .p-accordion .p-accordion-toggle-icon.pi-chevron-down::before {
    content: "\e90f";
  }
  .p-accordion .p-accordion-header.p-highlight .p-accordion-header-link {
    padding-bottom: calc(1rem + 2px);
    border-bottom: 0 none;
  }
  .p-accordion .p-accordion-header:not(.p-disabled).p-highlight:hover .p-accordion-header-link {
    border-bottom: 0 none;
  }

  .p-inline-message.p-inline-message-info {
    border-color: #a3d7e6;
  }
  .p-inline-message.p-inline-message-success {
    border-color: #bfd47f;
  }
  .p-inline-message.p-inline-message-warn {
    border-color: #ff9c3e;
  }
  .p-inline-message.p-inline-message-error {
    border-color: #e6a3b2;
  }

  .p-inputtext:enabled:focus {
    box-shadow: none;
  }

  .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .p-multiselect:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .p-cascadeselect:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .p-autocomplete.p-autocomplete-multiple:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .p-chips .p-chips-multiple-container:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .p-orderlist .p-orderlist-list {
    border-top: 0 none;
  }

  .p-picklist .p-picklist-list {
    border-top: 0 none;
  }

  .p-panelmenu .p-panelmenu-icon.pi-chevron-right, .p-panelmenu .p-panelmenu-icon.pi-chevron-down {
    order: 10;
    margin-left: auto;
  }
  .p-panelmenu .p-panelmenu-icon.pi-chevron-right::before {
    content: "\e90d";
  }
  .p-panelmenu .p-panelmenu-icon.pi-chevron-down::before {
    content: "\e90f";
  }
  .p-panelmenu .p-panelmenu-header.p-highlight > a {
    padding-bottom: calc(1rem + 2px);
    border-bottom: 0 none;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-highlight):not(.p-disabled) > a:hover {
    padding-bottom: calc(1rem + 2px);
    border-bottom: 0 none;
  }

  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-top > td {
    box-shadow: inset 0 2px 0 0 #9eade6;
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {
    box-shadow: inset 0 -2px 0 0 #9eade6;
  }
}
