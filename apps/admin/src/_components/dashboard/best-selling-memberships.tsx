import { ITabsSection } from './tabs-section';
export type IBestMemberships = {
  topSellingMemberShips: {
    _count: {
      gymMembershipId: number | null;
    };
    gymMembershipId: string;
  }[];
  bestSellingMemberShips: {
    name: string;
    id: string;
    gymMembershipBookings: {
      amountInCents: number;
    }[];
  }[];
};
const BestSellingMembershipsTab = ({ bestSellingMemberShips, topSellingMemberShips }: IBestMemberships) => {
  const filteredMemberships = topSellingMemberShips.map((i) => {
    const filtered = bestSellingMemberShips.find((b) => b.id === i.gymMembershipId);
    return { ...i, filtered };
  });
  return (
    <>
      <>
        {filteredMemberships.map((b) => (
          <>
            <ul className="list-none p-0 m-0">
              <li className="flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4">
                <div>
                  <span className="text-900 font-medium mr-2 mb-1 md:mb-0">{b.filtered?.name}</span>
                  {/* <div className="mt-1 text-600">{b.filtered?.product.category.name}</div> */}
                  <div className="text-500  font-medium">Price : ₹ {(b.filtered?.gymMembershipBookings[0].amountInCents || 0) / 100}</div>
                </div>
                <div className="mt-2 md:mt-0 flex align-items-center">
                  <span className="font-base">{b._count.gymMembershipId} times</span>
                </div>
              </li>
            </ul>
          </>
        ))}
      </>
    </>
  );
};

export default BestSellingMembershipsTab;
