type IBestSellingProducts = {
  bestSellingProducts: {
    id: string;
    name: string;
    product: {
      id: string;
      name: string;
      category: {
        id: string;
        name: string;
      };
    };
    lineItems: {
      perUnitPriceInCent: number;
      quantity: number;
    }[];
  }[];
  topSellingProducts: {
    _sum: {
      quantity: number | null;
    };
    productVariantId: string;
  }[];
};
const BestSellingProductsTabs = ({ bestSellingProducts, topSellingProducts }: IBestSellingProducts) => {
  const filteredProducts = topSellingProducts.map((b) => {
    const filtered = bestSellingProducts.find((i) => b.productVariantId === i.id);
    return { ...b, filtered };
  });

  return (
    <>
      {filteredProducts.map((b, index) => (
        <ul key={index} className="list-none p-0 m-0">
          <li className="flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4">
            <div>
              <span className="text-900 font-medium mr-2 mb-1 md:mb-0">
                {b.filtered?.product.name} <span className="text-500 font-base">({b.filtered?.name})</span>
              </span>
              <div className="mt-1 text-600">{b.filtered?.product.category.name}</div>
              {/* <div className="text-500  font-medium">Per unit Price : ₹ {(b.filtered?.lineItems[0]?.perUnitPriceInCent || 0) / 100}</div> */}
              {/* wrong as it is displaying the actual price without the discount applying on it */}
            </div>
            <div className="mt-2 md:mt-0 flex align-items-center">
              <span className="font-base">{b._sum.quantity} items</span>
            </div>
          </li>
        </ul>
      ))}
    </>
  );
};

export default BestSellingProductsTabs;
