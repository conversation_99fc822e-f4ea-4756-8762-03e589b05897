import { ITabsSection } from './tabs-section';
type IBestMemberships = {
  bestBookedVenues: {
    id: string;
    name: string;
    venue: {
      id: string;
      name: string;
    };
    venueMembershipBooking: {
      amountInCents: number;
    }[];
  }[];
  topBookedVenues: {
    _count: {
      venueGameMemberShipId: number | null;
    };
    venueGameMemberShipId: string;
  }[];
};
const BestSellingVenueMemberships = ({ bestBookedVenues, topBookedVenues }: IBestMemberships) => {
  const filteredMemberships = topBookedVenues.map((i) => {
    const filtered = bestBookedVenues.find((b) => b.id === i.venueGameMemberShipId);
    return { ...i, filtered };
  });
  return (
    <>
      {filteredMemberships.map((b) => (
        <>
          <ul className="list-none p-0 m-0">
            <li className="flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4">
              <div>
                <span className="text-900 font-medium mr-2 mb-1 md:mb-0">{b.filtered?.name}</span>
                <div className="mt-1 text-600">{b.filtered?.venue.name}</div>
                <div className="text-500  font-medium">Price : ₹ {(b.filtered?.venueMembershipBooking[0].amountInCents || 0) / 100}</div>
              </div>
              <div className="mt-2 md:mt-0 flex align-items-center">
                <span className="font-base">{b._count.venueGameMemberShipId} times</span>
              </div>
            </li>
          </ul>
        </>
      ))}
    </>
  );
};

export default BestSellingVenueMemberships;
