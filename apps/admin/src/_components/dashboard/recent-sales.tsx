'use client';
import { Dropdown } from 'primereact/dropdown';
import { DataTable } from 'primereact/datatable';
import { useEffect, useState } from 'react';
import { Demo } from '@/types';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { ProductService } from '../../demo/service/ProductService';
import { IOrder } from '@/src/_models/order.model';
import { IActiveLineItem, IOrderDetail } from '@/src/app/(main)/manage-products/orders/orders-table';
import OrderDialog from '@/src/app/(main)/manage-products/orders/order-dialog';
import { format } from 'date-fns';
import { api } from '@/src/trpc/react';

type IRecentSalesGymMembership = {
  id: string;
  startDate: Date;
  endDate: Date;
  amountInCents: number;
  user: {
    id: string;
    firstName: string;
    email: string;
  };
  gymMembership: {
    id: string;
    name: string;
  };
};
type IRecentSalesVenueMemberships = {
  id: string;
  startDate: Date;
  endDate: Date;
  amountInCents: number;
  user: {
    id: string;
    firstName: string;
    email: string;
  };
  venueGameMemberShip: {
    id: string;
    name: string;
  };
};

type IRecentSales = {
  orders: IOrderDetail[];
  gymMemberships: IRecentSalesGymMembership[];
  venueMemberships: IRecentSalesVenueMemberships[];
};
const RecentSales = ({ orders, gymMemberships, venueMemberships }: IRecentSales) => {
  const { data: users } = api.users.getUsers.useQuery();
  const [selectedCategory, setSelectedCategory] = useState('Products');
  const [products, setProducts] = useState<Demo.Product[]>([]);
  const [visible, setVisible] = useState(false);
  const [currentOrderInfo, setCurrentOrderInfo] = useState<string[]>([]);
  const [currentLineItems, setCurrentLineItems] = useState<IActiveLineItem[]>([]);
  const categories = ['Gym Memberships', 'Venue Memberships', 'Products'];

  useEffect(() => {
    ProductService.getProductsSmall().then((data) => setProducts(data));
  }, []);

  const productImage = (rowData: IOrderDetail) => {
    return (
      <>
        <img className="shadow-2" src={rowData.lineItems[0].productVariant.product?.media[0]?.media?.fileUrl || '/not-available.jpg'} alt="image" width="50" />
      </>
    );
  };
  const userDetail = (rowData: IOrderDetail) => {
    const user = users?.find((i) => i.id === rowData.userId);
    return (
      <>
        <div>{user?.firstName}</div>
      </>
    );
  };
  const totalInCent = (rowData: IOrderDetail) => {
    return <>{`₹${rowData.totalInCent / 100}`}</>;
  };
  const actionBodyTemplate = (rowData: IOrderDetail) => {
    return (
      <>
        <Button icon="pi pi-folder-open" onClick={() => showLineItemsDialog(rowData)} text />
      </>
    );
  };
  const showLineItemsDialog = (order: IOrderDetail) => {
    const orderInfo = [
      `SubTotal: Rs. ${order.subTotalInCent / 100}`,
      `Taxes: Rs. ${order.taxesInCent / 100}`,
      `Delivery Fees: Rs. ${order.deliveryFeesInCent / 100}`,
      `Total: Rs. ${order.totalInCent / 100}`,
      `Address: ${order.address.name}, ${order.address.houseNo}, ${order.address.area}, ${order.address.city}, ${order.address.pincode}`
    ];
    setCurrentOrderInfo(orderInfo);
    setCurrentLineItems(order.lineItems);
    setVisible(true);
  };

  const gymMembershipDetails = (rowData: IRecentSalesGymMembership) => {
    return (
      <>
        <div>{rowData.gymMembership.name}</div>
        <span className="text-sm">{format(rowData.startDate, 'dd MMM yyy')}</span>
        {' - '}
        <span className="text-sm">{format(rowData.endDate, 'dd MMM yyy')}</span>
      </>
    );
  };

  const venueMembershipDetails = (rowData: IRecentSalesVenueMemberships) => {
    return (
      <>
        <div>{rowData.venueGameMemberShip.name}</div>
        <span className="text-sm">{format(rowData.startDate, 'dd MMM yyy')}</span>
        {' - '}
        <span className="text-sm">{format(rowData.endDate, 'dd MMM yyy')}</span>
      </>
    );
  };

  return (
    <div className="card">
      <div className="flex justify-content-between align-items-center">
        <h5>Recent Sales</h5>
        <Dropdown value={selectedCategory} onChange={(e) => setSelectedCategory(e.value)} options={categories} optionLabel="" placeholder="Select Category" className="w-max mb-4" />
      </div>

      {/* selected category is products */}
      {selectedCategory === 'Products' && (
        <>
          <DataTable stripedRows value={orders} rows={5} paginator scrollable scrollHeight="500px">
            <Column header="Image" body={productImage} />
            <Column header="Username" body={userDetail} sortable style={{ width: '35%' }} />
            <Column header="Price" sortable style={{ width: '35%' }} body={totalInCent} />
            <Column header="LineItems" style={{ width: '15%' }} body={actionBodyTemplate} />
          </DataTable>
          <OrderDialog visible={visible} currentLineItems={currentLineItems} onHide={() => setVisible(false)} currentOrderInfo={currentOrderInfo} />
        </>
      )}

      {/* selected category is game memberships */}
      {selectedCategory === 'Gym Memberships' && (
        <DataTable stripedRows value={gymMemberships} rows={5} paginator scrollable scrollHeight="500px">
          <Column header="MembershipName" body={gymMembershipDetails} style={{ width: '35%' }} />
          <Column header="Username" sortable style={{ width: '35%' }} body={(rowData: IRecentSalesGymMembership) => rowData.user.firstName} />
          <Column header="Price" sortable style={{ width: '15%' }} body={(rowData: IRecentSalesGymMembership) => rowData.amountInCents / 100} />
        </DataTable>
      )}

      {/* selected category is venue memberships */}
      {selectedCategory === 'Venue Memberships' && (
        <DataTable stripedRows value={venueMemberships} rows={5} paginator scrollable scrollHeight="500px">
          <Column header="MembershipName" body={venueMembershipDetails} style={{ width: '35%' }} />
          <Column header="Username" sortable style={{ width: '35%' }} body={(rowData: IRecentSalesVenueMemberships) => rowData.user.firstName} />
          <Column header="Price" sortable style={{ width: '15%' }} body={(rowData: IRecentSalesVenueMemberships) => rowData.amountInCents / 100} />
        </DataTable>
      )}
    </div>
  );
};

export default RecentSales;
