'use client';
import { TabView, TabPanel } from 'primereact/tabview';
import { Dropdown } from 'primereact/dropdown';
import { useState } from 'react';

import BestSellingProductsTabs from './best-selling-products-tab';
import HotDealsTab from './hot-deals-tab';
import NewArrivalTab from './new-arrival-tab';
import BestSellingMembershipsTab from './best-selling-memberships';
import BestSellingVenueMemberships from './best-selling-venue-memberships';
import BestBookedSlots from './best-booked-slots';

export type ITabsSection = {
  bestSellingProducts: {
    id: string;
    name: string;
    product: {
      id: string;
      name: string;
      category: {
        id: string;
        name: string;
      };
    };
    lineItems: {
      perUnitPriceInCent: number;
      quantity: number;
    }[];
  }[];
  topSellingProducts: {
    _sum: {
      quantity: number | null;
    };
    productVariantId: string;
  }[];
  topSellingMemberShips: {
    _count: {
      gymMembershipId: number | null;
    };
    gymMembershipId: string;
  }[];
  bestSellingMemberShips: {
    name: string;
    id: string;
    gymMembershipBookings: {
      amountInCents: number;
    }[];
  }[];
  bestBookedVenues: {
    id: string;
    name: string;
    venue: {
      id: string;
      name: string;
    };
    venueMembershipBooking: {
      amountInCents: number;
    }[];
  }[];
  topBookedVenues: {
    _count: {
      venueGameMemberShipId: number | null;
    };
    venueGameMemberShipId: string;
  }[];
  topSlotBooking: {
    _count: {
      venueId: number;
    };
    venueId: string;
  }[];
  bestBookedSlots: {
    id: string;
    name: string;
  }[];
};
const TabsSection = ({ bestSellingProducts, topSellingProducts, topSellingMemberShips, bestSellingMemberShips, topBookedVenues, bestBookedVenues, topSlotBooking, bestBookedSlots }: ITabsSection) => {
  const [selectedCategory, setSelectedCategory] = useState('Products');
  const categories = ['Gym Memberships', 'Venue Memberships', 'Products', 'Book Slots'];

  return (
    <>
      <div className="card">
        <div className="flex justify-content-between align-items-center">
          <h5>Best Sellers</h5>
          <Dropdown value={selectedCategory} onChange={(e) => setSelectedCategory(e.value)} options={categories} optionLabel="" placeholder="Select Category" className="w-max mb-4" />
        </div>
        {selectedCategory === 'Products' && <BestSellingProductsTabs bestSellingProducts={bestSellingProducts} topSellingProducts={topSellingProducts} />}
        {selectedCategory === 'Gym Memberships' && <BestSellingMembershipsTab bestSellingMemberShips={bestSellingMemberShips} topSellingMemberShips={topSellingMemberShips} />}
        {selectedCategory === 'Venue Memberships' && <BestSellingVenueMemberships bestBookedVenues={bestBookedVenues} topBookedVenues={topBookedVenues} />}
        {selectedCategory === 'Book Slots' && <BestBookedSlots topSlotBooking={topSlotBooking} bestBookedSlots={bestBookedSlots} />}
      </div>
    </>
  );
};

export default TabsSection;
