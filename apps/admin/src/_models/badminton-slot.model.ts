export type IVenueGameMembership = {
    id?: string;
    name: string;
    amountInCents: number;
    monthsInPlan?: number;
    daysInPlan: number;
    totalAvailableCourts: number | null;
    discountInPercentage: number | null;
    discountEndDate: Date | null;
    active: boolean;
    venueId: string;
    gameId: string;
    features: { name: string }[];
    createdAt: Date;
    updatedAt: Date;
};

export type IVenueGameMembershipForm = Omit<IVenueGameMembership, 'createdAt' | 'updatedAt'>;
