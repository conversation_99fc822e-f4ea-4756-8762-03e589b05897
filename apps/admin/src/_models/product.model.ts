import { IMedia } from '@/src/_models/media.model';

export type ICurrency = {
  id: string;
  name: string;
  symbol: string;
};

export type IProduct = {
  id: string;
  name: string;
  hsn: string | null;
  description: string;
  slug: string;
  seoTitle: string | null;
  seoDescription: string | null;
  seoKeywords: string[];
  avgRating: string;
  totalReviews: number;
  active: boolean;
  bestseller: boolean;
  brandId: string | null;
  brand: {
    id: string;
    name: string;
  } | null;
  categoryId: string;
  category: {
    id: string;
    name: string;
  } | null;
  productBenefits: IProductBenefit[];
  productStats: IProductStats[];
  productVariants: IProductVariant[];
  media: {
    order: number;
    imageAltText: string | null;
    comment: string | null;
    mediaId: string;
    productId: string;
    media: {
      id: string;
      fileUrl: string | null;
      fileKey: string;
    };
  }[];
  createdAt: Date;
  updatedAt: Date;
  veg: boolean | null;
};

export type IProductNew = Omit<IProduct, 'avgRating' | 'isPercentage'>;
export type IProductForm = Omit<IProduct, 'veg' | 'avgRating' | 'totalReviews' | 'category' | 'brand' | 'createdAt' | 'updatedAt'> & { veg?: boolean | string | null };

export type IProductBenefit = {
  id: string | null;
  benefit: string;
};

export type IProductStats = {
  id: string | null;
  title: string;
  stat: string;
};

export type IProductVariant = {
  id: string | null;
  sku: string | null;
  name: string;
  priceInCents: number;
  isPercentage?: boolean | null;
  discountInCents: number | null;
  discountInPercentage: number | null;
  discountEndDate: Date | null;
  weightInKgForShipping: number;
  // productVariantPrices: IProductVariantPrice[];
};

export type IProductVariantInventory = {
  id: string | null;
  available: number;
  onHand: number;
  damaged: number;
  qualityControl: number;
  safetyStock: number;
  other: number;
  commited: number;
};

export type IMediaOnProducts = {
  order: number;
  imageAltText: string | null;
  comment: string | null;
  productId: string;
  mediaId: string;
  media: IMedia;
};
