export type IUser = {
  id?: string;
  email: string;
  firstName: string;
  middleName: string | null;
  lastName: string | null;
  mobileNumber: string | null;
  gender: string | null;
  accountType: 'normal' | 'facebook' | 'google' | 'apple';
  active: boolean;
  password?: string;
};

export type IUserVenueBookingForm = Pick<IUser, 'id' | 'email' | 'firstName' | 'middleName' | 'lastName'>;

export type DeleteAccount = {
  id: string;
  user: IUser;
  userId: string;
  createdAt: Date;
  deletedAt?: Date;
};
