import { string } from 'zod';

export type IVenueBooking = {
  id: string;
  venueId: string;
  venue: {
    id: string;
    name: string;
  };
  // gameSlotId:string;
  gameSlots: {
    id: string;
    startTime: Date;
    endTime: Date;
    gameId: string;
    game: {
      id: string;
      name: string;
    };
  }[];
  userId: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string | null;
    mobileNumber: string | null;
  };
  subTotalInCents: number;
  discountInCents: number;
  totalAmount: number;
  comments: string | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
};

export type IVenueBookingForm = Omit<IVenueBooking, 'id' | 'venue' | 'user' | 'status' | 'createdAt' | 'updatedAt' | 'gameSlots' | 'subTotalInCents' | 'totalAmount'> & {
  date: Date;
  gameId: string;
  gameSlots: string[];
};
