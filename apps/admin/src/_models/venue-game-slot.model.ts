import { IGame } from '@/src/_models/game.model';
import { IVenue } from '@/src/_models/venue.model';

export type IVenueGameSlot = {
  id: string;
  gameId: string;
  game?: IGame;
  venueId: string;
  venue?: IVenue;
  startTime: Date;
  endTime: Date;
  availableCourts: number;
  bookedCourts: number;
  slotPrice: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
};

export type IVenueGameSlotForm = Omit<IVenueGameSlot, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>;
