export type IVenueMembership = {
    id: string;
    name: string;
    amountInCents: number;
    //   bookedCourts: number;
    totalAvailableCourts: number | null;
    daysInPlan: number;
    game: {
        id: string;
        name: string;
    };
};

export type IVenueMembershipBookingForm = {
    id?: string;
    startDate: Date;
    endDate: Date;
    userId: string;
    venueGameMemberShipId: string;
    comments: string;
    discountInCents: number;
};

export type IVenueMembershipBooking = {
    id: string;
    startDate: Date;
    endDate: Date;
    amountInCents: number;
    discountInCents: number;
    subTotalInCents: number;
    userId: string;
    bookedCourts: number;
    availableCourts: number;
    user: {
        id: string;
        firstName: string;
        lastName: string | null;
        email: string;
        mobileNumber: string | null;
    };
    venueGameMemberShip: {
        venue: {
            id: string;
            name: string;
        };
        game: {
            id: string;
            name: string;
        };
        id: string;
        name: string;
        amountInCents: number;
    };
    status: string;
    comments: string | null;
    createdAt: Date;
    updatedAt: Date;
    razorpay_refund_id?: string;
    refund_type?: string;
};
