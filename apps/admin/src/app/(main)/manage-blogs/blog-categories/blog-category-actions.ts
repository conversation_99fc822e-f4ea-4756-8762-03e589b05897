'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { ICategory } from '@/src/_models/category.model';
import { auth } from '@/src/server/auth';
import { IBlogCategory } from '@/src/_models/blog-category.model';

export const createBlogCategory = async (data: IBlogCategory) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { name, active, slug } = data;

  const FormData = z.object({
    name: z.string().min(1),
    active: z.boolean().optional(),
    slug: z.string()
  });

  const isValidData = FormData.parse({
    name,
    active,
    slug
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  await db.blogCategory.create({
    data: {
      name: name as string,
      active: active,
      slug: slug
    }
  });

  revalidatePath('/manage-blogs/blog-categories');
  revalidatePath('/manage-blogs/blogs');
  return {
    message: 'BLog category created successfully'
  };
};

export const updateBlogCategory = async (data: IBlogCategory) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, name, active, slug } = data;

  const FormData = z.object({
    id: z.string(),
    name: z.string().min(1),
    active: z.boolean().optional(),
    slug: z.string()
  });

  const isValidData = FormData.parse({
    id,
    name,
    active,
    slug
  });

  await db.blogCategory.update({
    where: {
      id: id as string
    },
    data: {
      name: isValidData.name,
      active: isValidData.active,
      slug: isValidData.slug
    }
  });

  revalidatePath('/manage-blogs/blog-categories');
  revalidatePath('/manage-blogs/blogs');
  return {
    message: 'Blog category updated successfully'
  };
};
