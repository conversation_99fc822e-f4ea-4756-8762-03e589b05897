import { Controller, useForm } from 'react-hook-form';
import { InputText } from 'primereact/inputtext';
import { classNames } from 'primereact/utils';
import { Checkbox } from 'primereact/checkbox';
import { But<PERSON> } from 'primereact/button';
import SubmitButton from '@/src/_components/shared/submit-button';
import React, { useEffect } from 'react';
import { createBlogCategory, updateBlogCategory } from '@/src/app/(main)/manage-blogs/blog-categories/blog-category-actions';
import useToastContext from '@/src/_hooks/useToast';
import { slugifyName } from '@/src/lib/utils';
import { IBlogCategory, IBlogCategoryForm } from '@/src/_models/blog-category.model';

type BlogCategoryFormProps = {
  blogCategory: IBlogCategoryForm;
  hideDialog: () => void;
};

const BlogCategoryForm = ({ blogCategory, hideDialog }: BlogCategoryFormProps) => {
  const { showToast } = useToastContext();
  const {
    control,
    setValue,
    handleSubmit,
    reset,
    watch,
    getValues,
    formState: { isLoading }
  } = useForm<IBlogCategory>({
    defaultValues: blogCategory
  });

  useEffect(() => {
    reset({ ...blogCategory });
  }, [blogCategory]);

  const callServerAction = (data: IBlogCategory) => {
    if (blogCategory?.id) {
      return updateBlogCategory(data);
    } else {
      return createBlogCategory(data);
    }
  };

  const submitForm = (data: IBlogCategory) => {
    return callServerAction(data)
      .then((resp) => {
        if (resp.error) {
          showToast('error', 'Error', resp.error);
        }
        if (resp.message) {
          showToast('success', 'Successful', resp.message);
          hideDialog();
        }
      })
      .catch((err) => {
        showToast('error', 'Error', err.message);
        // console.log(err);
      })
      .finally(() => {});
  };

  const generateSlug = () => {
    const name = getValues('name');
    setValue('slug', slugifyName(name));
  };

  return (
    <>
      <form onSubmit={handleSubmit(submitForm)} noValidate={true}>
        <div className="field">
          <label htmlFor="name">Name</label>
          <Controller
            name="name"
            control={control}
            rules={{
              required: {
                value: true,
                message: 'Name is required.'
              }
            }}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputText
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    // onChange={(e) => {
                    //   const slug = slugifyName(e.target.value);
                    //   setValue('slug', slug);
                    //   field.onChange(e.target.value);
                    // }}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>
        <div className="field">
          <label htmlFor="slug">
            Slug &nbsp;
            <a className="cursor-pointer" onClick={() => generateSlug()}>
              Generate Slug
            </a>
          </label>

          <Controller
            name="slug"
            control={control}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputText
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>

        <div className="field">
          <Controller
            name="active"
            control={control}
            render={({ field, fieldState }) => {
              return (
                <div className="flex gap-2 align-bottom">
                  <Checkbox checked={field.value} {...field}  onChange={(v) => setValue('active', !!v.checked)} />
                  <label htmlFor="active">Active</label>
                </div>
              );
            }}
          />
        </div>

        <div className="flex flex-row gap-4">
          <Button label="Cancel" type="reset" icon="pi pi-times" severity="danger" onClick={hideDialog} />
          <SubmitButton label="Save" icon="pi pi-check" />
        </div>
      </form>
    </>
  );
};

export default BlogCategoryForm;
