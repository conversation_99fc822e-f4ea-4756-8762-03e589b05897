'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { ICategory } from '@/src/_models/category.model';
import { auth } from '@/src/server/auth';
import { IBlogCategory } from '@/src/_models/blog-category.model';
import { IBlogForm } from '@/src/_models/blog.model';

export const createBlog = async (data: IBlogForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { title, body, author, active, slug, categoryId, mediaId, popularBlog, seoTitle, seoDescription, seoKeywords } = data;

  const FormData = z.object({
    title: z.string().min(1),
    active: z.boolean().optional(),
    popularBlog: z.boolean(),
    author: z.string(),
    body: z.string(),
    slug: z.string(),
    categoryId: z.string(),
    mediaId: z.string(),
    seoTitle: z.string(),
    seoDescription: z.string(),
    seoKeywords: z.array(z.string())
  });

  const isValidData = FormData.parse({
    title,
    body,
    author,
    active,
    slug,
    mediaId,
    categoryId,
    popularBlog,
    seoTitle,
    seoDescription,
    seoKeywords
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  try {
    await db.blog.create({
      data: {
        title: isValidData.title,
        body: isValidData.body,
        author: isValidData.author,
        active: !!isValidData.active,
        slug: isValidData.slug,
        categoryId: isValidData.categoryId,
        mediaId: isValidData.mediaId,
        popularBlog: isValidData.popularBlog,
        seoTitle: isValidData.seoTitle,
        seoDescription: isValidData.seoDescription,
        seoKeywords: isValidData.seoKeywords
      }
    });

    revalidatePath('/manage-blogs/blogs');
    return {
      message: 'Blog created successfully'
    };
  } catch (error) {
    return {
      error: 'Error'
    };
    // console.log('blogCreation', error);
  }
};

export const updateBlog = async (data: IBlogForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, title, body, author, active, slug, categoryId, mediaId, popularBlog, seoTitle, seoDescription, seoKeywords } = data;

  const FormData = z.object({
    id: z.string().min(1),
    title: z.string().min(1),
    active: z.boolean().optional(),
    author: z.string(),
    body: z.string(),
    slug: z.string(),
    categoryId: z.string(),
    mediaId: z.string(),
    popularBlog: z.boolean().optional(),
    seoTitle: z.string(),
    seoDescription: z.string(),
    seoKeywords: z.array(z.string())
  });

  const isValidData = FormData.parse({
    id,
    title,
    body,
    author,
    active,
    slug,
    mediaId,
    categoryId,
    popularBlog,
    seoTitle,
    seoDescription,
    seoKeywords
  });

  await db.blog.update({
    where: {
      id: isValidData.id
    },
    data: {
      title: isValidData.title,
      body: isValidData.body,
      author: isValidData.author,
      active: !!isValidData.active,
      slug: isValidData.slug,
      categoryId: isValidData.categoryId,
      mediaId: isValidData.mediaId,
      popularBlog: isValidData.popularBlog,
      seoTitle: isValidData.seoTitle,
      seoDescription: isValidData.seoDescription,
      seoKeywords: isValidData.seoKeywords
    }
  });

  revalidatePath('/manage-blogs/blogs');
  return {
    message: 'Blog updated successfully'
  };
};
