'use client';
import { <PERSON><PERSON> } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Toolbar } from 'primereact/toolbar';
import React, { ChangeEvent, useRef, useState } from 'react';
import { Demo } from '@/types';
import { Tag } from 'primereact/tag';
import BlogForm from '@/src/app/(main)/manage-blogs/blogs/blog-form';
import { FilterMatchMode } from 'primereact/api';
import { IBlog, IBlogForm } from '@/src/_models/blog.model';
import { IBlogCategory, IBlogCategorySelect } from '@/src/_models/blog-category.model';
import { format } from 'date-fns';

const BlogsTable = ({ blogs, blogCategories }: { blogs: IBlog[]; blogCategories: IBlogCategorySelect[] }) => {
  const emptyBlog: IBlogForm = { id: '', title: '', slug: '', author: '', body: '', categoryId: '', active: false, media: null, mediaId: '', popularBlog: null, seoTitle: '', seoDescription: '', seoKeywords: [] };
  const [blogDialog, setBlogDialog] = useState(false);
  const [blog, setBlog] = useState<IBlogForm>(emptyBlog);
  const [selectedBlogs, setSelectedBlogs] = useState(null);
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS }
  });
  const [globalFilter, setGlobalFilter] = useState('');
  const dt = useRef<DataTable<any>>(null);

  const openNew = () => {
    setBlog({ ...emptyBlog });
    setBlogDialog(true);
  };

  const hideDialog = () => {
    setBlogDialog(false);
  };

  const editBlog = (blog: IBlogForm) => {
    setBlog({ ...blog });
    setBlogDialog(true);
  };

  const exportCSV = () => {
    dt.current?.exportCSV();
  };

  const leftToolbarTemplate = () => {
    return (
      <React.Fragment>
        <div className="my-2">
          <Button label="New" icon="pi pi-plus" severity="success" className=" mr-2" onClick={openNew} />
        </div>
      </React.Fragment>
    );
  };

  const rightToolbarTemplate = () => {
    return (
      <React.Fragment>
        {/*<FileUpload mode="basic" accept="image/*" maxFileSize={1000000} chooseLabel="Import" className="mr-2 inline-block" />*/}
        <Button label="Export" icon="pi pi-upload" severity="help" onClick={exportCSV} />
      </React.Fragment>
    );
  };

  const idBodyTemplate = (rowData: Demo.Product) => {
    return (
      <>
        <span className="p-column-title">Id</span>
        {rowData.id}
      </>
    );
  };

  const titleBodyTemplate = (rowData: IBlog) => {
    return (
      <>
        <span className="p-column-title">Title</span>
        {rowData.title}
      </>
    );
  };

  const authorBodyTemplate = (rowData: IBlog) => {
    return (
      <>
        <span className="p-column-title">Author</span>
        {rowData.author}
      </>
    );
  };

  const slugBodyTemplate = (rowData: IBlog) => {
    return (
      <>
        <span className="p-column-title">Slug</span>
        {rowData.slug}
      </>
    );
  };

  const actionBodyTemplate = (rowData: typeof emptyBlog) => {
    return (
      <>
        <Button icon="pi pi-pencil" rounded severity="success" className="mr-2" onClick={() => editBlog(rowData)} />
      </>
    );
  };

  const onGlobalFilterChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let _filters: any = { ...filters };

    _filters.global.value = value;

    setFilters(_filters);
    setGlobalFilter(value);
  };

  const header = (
    <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
      <h5 className="m-0">Manage Blogs</h5>
      <span className="block mt-2 md:mt-0 p-input-icon-left">
        <i className="pi pi-search" />
        <InputText type="search" value={globalFilter} onChange={onGlobalFilterChange} placeholder="Search..." />
      </span>
    </div>
  );

  const activeBodyTemplate = (rowData: IBlog) => {
    return (
      <>
        <span className="p-column-title">Active</span>
        {rowData.active ? <Tag severity="success">Yes</Tag> : <Tag severity="danger">No</Tag>}
      </>
    );
  };

  const createdAtBodyTemplate = (rowData: IBlog) => {
    return (
      <>
        <span className="p-column-title">Created At</span>
        {rowData.createdAt && format(rowData.createdAt, 'd MMM yyyy hh:mm aaa')}
      </>
    );
  };

  const updatedAtBodyTemplate = (rowData: IBlog) => {
    return (
      <>
        <span className="p-column-title">Updated At</span>
        {rowData.updatedAt && format(rowData.updatedAt, 'd MMM yyyy hh:mm aaa')}
      </>
    );
  };

  return (
    <div className="grid crud-demo">
      <div className="col-12">
        <div className="card">
          <Toolbar className="mb-4" start={leftToolbarTemplate} end={rightToolbarTemplate}></Toolbar>

          <DataTable
            stripedRows
            ref={dt}
            value={blogs}
            selection={selectedBlogs}
            onSelectionChange={(e) => setSelectedBlogs(e.value as any)}
            dataKey="id"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25]}
            className="datatable-responsive"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            filters={filters}
            globalFilterFields={['id', 'name']}
            emptyMessage="No blog found."
            header={header}
            exportFilename="Blogs"
          >
            <Column field="id" header="Id" sortable body={idBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="title" header="Title" sortable body={titleBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="author" header="Author" sortable body={authorBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="slug" header="Slug" sortable body={slugBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="active" header="Active" sortable body={activeBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="createdAt" header="Created At" sortable body={createdAtBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="updatedAt" header="Updated" sortable body={updatedAtBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column body={actionBodyTemplate} headerStyle={{ minWidth: '10rem' }} frozen={true}></Column>
          </DataTable>

          <Dialog visible={blogDialog} style={{ width: '50vw' }} header="Blog Details" modal className="p-fluid" onHide={hideDialog}>
            <BlogForm blog={blog} blogCategories={blogCategories} hideDialog={hideDialog} />
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default BlogsTable;
