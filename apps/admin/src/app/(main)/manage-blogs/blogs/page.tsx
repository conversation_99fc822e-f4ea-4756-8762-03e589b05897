// 'use server';

import { db } from '@/src/server/db';
import BlogsTable from '@/src/app/(main)/manage-blogs/blogs/blogs-table';

const BlogsPage = async () => {
  const blogCategories = await db.blogCategory.findMany({
    select: {
      id: true,
      name: true
    },
    where: {
      deletedAt: null,
      active: true
    },
    orderBy: {
      name: 'asc'
    }
  });

  const blogs = await db.blog.findMany({
    select: {
      id: true,
      title: true,
      author: true,
      body: true,
      seoDescription: true,
      seoKeywords: true,
      seoTitle: true,
      popularBlog: true,
      slug: true,
      active: true,
      createdAt: true,
      updatedAt: true,
      mediaId: true,
      media: true,
      categoryId: true,
      category: {
        select: {
          id: true,
          name: true
        }
      }
    },
    where: {
      deletedAt: null
    },
    orderBy: {
      title: 'asc'
    }
  });
  return <BlogsTable blogs={blogs} blogCategories={blogCategories} />;
};

export const dynamic = 'force-dynamic';

export default BlogsPage;
