'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { ICategory } from '@/src/_models/category.model';
import { auth } from '@/src/server/auth';
import { IBlogCategory } from '@/src/_models/blog-category.model';
import { IBlogForm } from '@/src/_models/blog.model';
import { IHomepageBanner, IHomepageBannerForm } from '@/src/_models/homepage-banner.model';

export const createHomePageBanner = async (data: IHomepageBannerForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { order, url, active, mediaId } = data;

  const FormData = z.object({
    order: z.number(),
    url: z.string().nullable(),
    active: z.boolean(),
    mediaId: z.string()
  });

  const isValidData = FormData.parse({
    order,
    url,
    active,
    mediaId
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  await db.homeBanner.create({
    data: {
      order: isValidData.order,
      url: isValidData.url,
      active: isValidData.active,
      mediaId: isValidData.mediaId
    }
  });

  revalidatePath('/manage-blogs/homepage-banners');
  return {
    message: 'Homepage banner created successfully'
  };
};

export const updateHomepageBanner = async (data: IHomepageBannerForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, order, url, active, mediaId } = data;

  const FormData = z.object({
    id: z.string(),
    order: z.number(),
    url: z.string().nullable(),
    active: z.boolean(),
    mediaId: z.string()
  });

  const isValidData = FormData.parse({
    id,
    order,
    url,
    active,
    mediaId
  });

  await db.homeBanner.update({
    where: {
      id: isValidData.id
    },
    data: {
      order: isValidData.order,
      url: isValidData.url,
      active: isValidData.active,
      mediaId: isValidData.mediaId
    }
  });

  revalidatePath('/manage-blogs/homepage-banners');
  return {
    message: 'Homepage Banner updated successfully'
  };
};
