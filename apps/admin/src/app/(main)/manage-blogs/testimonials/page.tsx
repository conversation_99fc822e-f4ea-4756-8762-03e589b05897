// 'use server';

export const dynamic = 'force-dynamic';

import { db } from '@/src/server/db';
import TestimonialsTable from '@/src/app/(main)/manage-blogs/testimonials/testimonials-table';

const TestimonialsPage = async () => {
  const testimonials = await db.testimonials.findMany({
    select: {
      id: true,
      title: true,
      name: true,
      body: true,
      media: true,
      mediaId: true,
      active: true,
      slug: true,
      createdAt: true,
      updatedAt: true,
      avgRating: true
    },
    where: {
      deletedAt: null
    },
    orderBy: {
      title: 'asc'
    }
  });
  return <TestimonialsTable testimonials={testimonials.map((t) => ({ ...t, avgRating: t.avgRating.toFixed(1) }))} />;
};

export default TestimonialsPage;
