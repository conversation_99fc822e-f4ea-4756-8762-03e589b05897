'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { ICategory } from '@/src/_models/category.model';
import { auth } from '@/src/server/auth';
import { IBlogCategory } from '@/src/_models/blog-category.model';
import { IBlogForm } from '@/src/_models/blog.model';
import { ITestimonialForm } from '@/src/_models/testimonial.model';

export const createTestimonial = async (data: ITestimonialForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { title, name, body, slug, active, mediaId } = data;

  const FormData = z.object({
    title: z.string().min(1),
    active: z.boolean().optional(),
    name: z.string(),
    body: z.string(),
    slug: z.string(),
    mediaId: z.string()
  });

  const isValidData = FormData.parse({
    title,
    name,
    slug,
    body,
    active,
    mediaId
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  await db.testimonials.create({
    data: {
      title: isValidData.title,
      name: isValidData.name,
      active: !!isValidData.active,
      body: isValidData.body,
      slug: isValidData.slug,
      mediaId: isValidData.mediaId
    }
  });

  revalidatePath('/manage-blogs/testimonials');
  return {
    message: 'Testimonial created successfully'
  };
};

export const updateTestimonial = async (data: ITestimonialForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, title, name, slug, body, active, mediaId } = data;

  const FormData = z.object({
    id: z.string().min(1),
    title: z.string().min(1),
    active: z.boolean().optional(),
    name: z.string(),
    body: z.string(),
    slug: z.string(),
    mediaId: z.string()
  });

  const isValidData = FormData.parse({
    id,
    title,
    name,
    body,
    slug,
    active,
    mediaId
  });

  await db.testimonials.update({
    where: {
      id: isValidData.id
    },
    data: {
      title: isValidData.title,
      name: isValidData.name,
      active: !!isValidData.active,
      slug: isValidData.slug,
      body: isValidData.body,
      mediaId: isValidData.mediaId
    }
  });

  revalidatePath('/manage-blogs/testimonials');
  return {
    message: 'Testimonial updated successfully'
  };
};

export const deleteTestimonial = async (testimonialId: string) => {
  try {
    await db.testimonials.update({
      data: {
        deletedAt: new Date()
      },
      where: {
        id: testimonialId
      }
    });
    revalidatePath('/manage-blogs/testimonials');
    return {
      message: 'Testimonials deleted successfully'
    };
  } catch (err) {
    return {
      error: 'Error in deleting testimonials'
    };
  }
};
