'use client';
import { <PERSON><PERSON> } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Toolbar } from 'primereact/toolbar';
import React, { ChangeEvent, useRef, useState } from 'react';
import { Demo } from '@/types';
import { Tag } from 'primereact/tag';
import TestimonialForm from '@/src/app/(main)/manage-blogs/testimonials/testimonial-form';
import { FilterMatchMode } from 'primereact/api';
import { ITestimonial, ITestimonialForm } from '@/src/_models/testimonial.model';
import { format } from 'date-fns';
import { IHomepageBanner } from '@/src/_models/homepage-banner.model';
import { Image } from 'primereact/image';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import useToastContext from '@/src/_hooks/useToast';
import { deleteTestimonial } from './testimonial-actions';

const TestimonialsTable = ({ testimonials }: { testimonials: ITestimonial[] }) => {
  const emptyTestimonial: ITestimonialForm = { id: '', title: '', name: '', body: '', slug: '', active: false, media: null, mediaId: '',avgRating:'' };
  const [testimonialDialog, setTestimonialDialog] = useState(false);
  const [testimonial, setTestimonial] = useState<ITestimonialForm>(emptyTestimonial);
  const { showToast } = useToastContext();
  const [selectedTestimonials, setSelectedTestimonials] = useState(null);
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS }
  });
  const [globalFilter, setGlobalFilter] = useState('');
  const dt = useRef<DataTable<any>>(null);

  const openNew = () => {
    setTestimonial({ ...emptyTestimonial });
    setTestimonialDialog(true);
  };

  const hideDialog = () => {
    setTestimonialDialog(false);
  };

  const editTestimonial = (testimonial: ITestimonialForm) => {
    setTestimonial({ ...testimonial });
    setTestimonialDialog(true);
  };

  const exportCSV = () => {
    dt.current?.exportCSV();
  };

  const leftToolbarTemplate = () => {
    return (
      <React.Fragment>
        <div className="my-2">
          <Button label="New" icon="pi pi-plus" severity="success" className=" mr-2" onClick={openNew} />
        </div>
      </React.Fragment>
    );
  };

  const rightToolbarTemplate = () => {
    return (
      <React.Fragment>
        {/*<FileUpload mode="basic" accept="image/*" maxFileSize={1000000} chooseLabel="Import" className="mr-2 inline-block" />*/}
        <Button label="Export" icon="pi pi-upload" severity="help" onClick={exportCSV} />
      </React.Fragment>
    );
  };

  const idBodyTemplate = (rowData: Demo.Product) => {
    return (
      <>
        <span className="p-column-title">Id</span>
        {rowData.id}
      </>
    );
  };

  const fileKeyBodyTemplate = (rowData: IHomepageBanner) => {
    if (!rowData.media?.fileUrl) {
      return;
    }
    if (rowData.media.mimeType?.includes('image')) {
      return (
        <div className="flex justify-content-center relative">
          <Image src={rowData.media.fileUrl} alt="Image" className="relative" width="100" height="auto" preview />
        </div>
      );
    }
    return (
      <a href={rowData.media.fileUrl} target="_blank">
        Uploaded File
      </a>
    );
  };

  const titleBodyTemplate = (rowData: ITestimonial) => {
    return (
      <>
        <span className="p-column-title">Title</span>
        {rowData.title}
      </>
    );
  };

  const nameBodyTemplate = (rowData: ITestimonial) => {
    return (
      <>
        <span className="p-column-title">Author</span>
        {rowData.name}
      </>
    );
  };

  const slugBodyTemplate = (rowData: ITestimonial) => {
    return (
      <>
        <span className="p-column-title">Slug</span>
        {rowData.slug}
      </>
    );
  };
  const reject = () => {
    console.log('rejected your request');
  };
  const accept = (testimonialId: string) => {
    return deleteTestimonial(testimonialId)
      .then((resp) => {
        if (resp.message) {
          showToast('success', 'Successful', resp.message);
        }
        if (resp.error) {
          showToast('error', 'Error', resp.error);
        }
      })
      .catch((err) => {
        showToast('error', 'Error', err.message);
      });
  };

const deleteRecord=(testimonial: ITestimonialForm)=>{
  confirmDialog({
    message: 'Do you want to delete this record?',
    header: 'Delete Confirmation',
    icon: 'pi pi-info-circle',
    acceptClassName: 'p-button-danger',
    accept: () => accept(testimonial.id!),
    reject
  });
}
  const actionBodyTemplate = (rowData: typeof emptyTestimonial) => {
    return (
      <>
        <Button icon="pi pi-pencil" rounded severity="success" className="mr-2" onClick={() => editTestimonial(rowData)} />
        <Button icon="pi pi-trash" rounded severity="danger"  className="mr-2" onClick={()=>deleteRecord(rowData)}></Button>

      </>
    );
  };

  const onGlobalFilterChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let _filters: any = { ...filters };

    _filters.global.value = value;

    setFilters(_filters);
    setGlobalFilter(value);
  };

  const header = (
    <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
      <h5 className="m-0">Manage Testimonials</h5>
      <span className="block mt-2 md:mt-0 p-input-icon-left">
        <i className="pi pi-search" />
        <InputText type="search" value={globalFilter} onChange={onGlobalFilterChange} placeholder="Search..." />
      </span>
    </div>
  );

  const activeBodyTemplate = (rowData: ITestimonial) => {
    return (
      <>
        <span className="p-column-title">Active</span>
        {rowData.active ? <Tag severity="success">Yes</Tag> : <Tag severity="danger">No</Tag>}
      </>
    );
  };

  const createdAtBodyTemplate = (rowData: ITestimonial) => {
    return (
      <>
        <span className="p-column-title">Created At</span>
        {rowData.createdAt && format(rowData.createdAt, 'd MMM yyyy hh:mm aaa')}
      </>
    );
  };

  const updatedAtBodyTemplate = (rowData: ITestimonial) => {
    return (
      <>
        <span className="p-column-title">Updated At</span>
        {rowData.updatedAt && format(rowData.updatedAt, 'd MMM yyyy hh:mm aaa')}
      </>
    );
  };

  return (
    <div className="grid crud-demo">
      <div className="col-12">
        <div className="card">
          <Toolbar className="mb-4" start={leftToolbarTemplate} end={rightToolbarTemplate}></Toolbar>

          <DataTable
            stripedRows
            ref={dt}
            value={testimonials}
            selection={selectedTestimonials}
            onSelectionChange={(e) => setSelectedTestimonials(e.value as any)}
            dataKey="id"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25]}
            className="datatable-responsive"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            filters={filters}
            globalFilterFields={['id', 'name']}
            emptyMessage="No testimonial found."
            header={header}
            exportFilename="Testimonials"
          >
            <Column field="id" header="Id" sortable body={idBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="fileKey" header="Image" sortable body={fileKeyBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="title" header="Title" sortable body={titleBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="name" header="Name" sortable body={nameBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="slug" header="Slug" sortable body={slugBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="active" header="Active" sortable body={activeBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="createdAt" header="Created At" sortable body={createdAtBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="updatedAt" header="Updated" sortable body={updatedAtBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column body={actionBodyTemplate} headerStyle={{ minWidth: '10rem' }} frozen={true}></Column>
          </DataTable>

          <Dialog visible={testimonialDialog} style={{ width: '50vw' }} header="Testimonial Details" modal className="p-fluid" onHide={hideDialog}>
            <TestimonialForm testimonial={testimonial} hideDialog={hideDialog} />
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default TestimonialsTable;
