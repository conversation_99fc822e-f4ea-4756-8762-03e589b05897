'use server';
import { db } from '@/src/server/db';
import { revalidatePath } from 'next/cache';
import { IGymMembershipBookingForm } from './gym-membership-booking-table';
import { Gender, GymMembershipBookingStatus } from '@repo/database';
import { addMonths } from 'date-fns';
export const createGymMembershipBooking = async (data: IGymMembershipBookingForm) => {
  const { userId, comments, gymMembershipId, gender, startDate, name, discountInCents, endDate } = data;
  const userDetails = await db.user.findUnique({
    where: {
      id: userId
    }
  });

  const gymMembership = await db.gymMembership.findUnique({
    where: {
      id: gymMembershipId
    }
  });

  try {
    if (!gymMembership) {
      return {
        error: 'Gym Membership does not exist.'
      };
    }
    console.log('amountIncents', gymMembership.amountInCents);
    console.log('discuntIncents in form is', discountInCents * 100);
    console.log('final amount is', gymMembership.amountInCents - discountInCents);

    const bookingCreated = await db.gymMembershipBooking.create({
      data: {
        startDate: startDate,
        endDate: addMonths(startDate, gymMembership.monthsInPlan!),
        amountInCents: gymMembership.amountInCents,
        createdAt: new Date(),
        gymMembershipId: gymMembershipId,
        userId: userId,
        comments: comments,
        gender: gender === 'Male' ? Gender.Male : gender === 'Female' ? Gender.Female : Gender.Others,
        name: userDetails?.firstName,
        status: GymMembershipBookingStatus.PAYMENT_OFFLINE,
        discountInCents: discountInCents * 100,
        subTotalInCents: gymMembership.amountInCents - discountInCents * 100

        // self: checked === false ? false : true
      }
    });
    revalidatePath('/manage-gym-memberships/gym-membership-bookings');
    return { success: 'Membership Booking created successfully' };
  } catch (err) {
    return { error: 'Error in booking membership' };
  }
};

export const updateGymMembershipBooking = async (data: IGymMembershipBookingForm) => {
  const gymMembership = await db.gymMembership.findUnique({
    where: {
      id: data.gymMembershipId
    }
  });
  try {
    await db.gymMembershipBooking.update({
      where: {
        id: data.id
      },
      data: {
        endDate: data.endDate
      }
    });
    revalidatePath('/manage-gym-memberships/gym-membership-bookings');
    return { success: 'Membership Booking updated successfully' };
  } catch (err) {
    return { error: 'Error in updating booked membership' };
  }
};
