'use client';

import { Dialog } from 'primereact/dialog';
import { IGymMembership, IGymMembershipBookingForm } from './gym-membership-booking-table';
import { Controller, useForm } from 'react-hook-form';
import { Dropdown } from 'primereact/dropdown';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';
import { useEffect, useState } from 'react';
import { createGymMembershipBooking, updateGymMembershipBooking } from './gym-membership-booking-actions';
import useToastContext from '@/src/_hooks/useToast';
import { Gender } from '@repo/database';
import { Calendar } from 'primereact/calendar';
import { InputText } from 'primereact/inputtext';
import { ToggleButton } from 'primereact/togglebutton';
import { InputNumber } from 'primereact/inputnumber';

type IGymMembershipBookingFormProps = {
  membershipBookings: IGymMembershipBookingForm;
  gymMemberships: IGymMembership[];
  users: {
    id: string;
    email: string;
    firstName: string;
    lastName: string | null;
  }[];
  gymMembershipBookingDialog: boolean;
  hideDialog: () => void;
};
const GymMembershipBookingForm = ({ membershipBookings, gymMemberships, users, gymMembershipBookingDialog, hideDialog }: IGymMembershipBookingFormProps) => {
  const [gymMembership, setgymMembership] = useState<IGymMembership>();

  const {
    watch,
    control,
    handleSubmit,
    reset,
    formState: { isLoading }
  } = useForm<IGymMembershipBookingForm>({
    defaultValues: {
      ...membershipBookings
    }
  });
  const { showToast } = useToastContext();

  useEffect(() => {
    reset({
      ...membershipBookings
    });
  }, [membershipBookings]);

  const callServerAction = (data: IGymMembershipBookingForm) => {
    if (data.id) {
      return updateGymMembershipBooking(data)
        .then(() => {
          reset();
          hideDialog();
          showToast('success', 'Success', 'Membership Booking updated successfully');
        })
        .catch(() => {
          showToast('error', 'Error', 'Something went wrong in updating membership.');
        });
    } else {
      return createGymMembershipBooking(data)
        .then(() => {
          reset();
          hideDialog();
          showToast('success', 'Success', 'Membership Booking created successfully');
        })
        .catch(() => {
          showToast('error', 'Error', 'Something went wrong.');
        });
    }
  };
  const submitForm = (data: IGymMembershipBookingForm) => {
    console.log('form data is', data);
    return callServerAction(data);
  };

  const genderOptions = [
    { id: Gender.Female, name: Gender.Female },

    { id: Gender.Male, name: Gender.Male },

    {
      id: Gender.Others,
      name: Gender.Others
    }
  ];
  return (
    <>
      <Dialog
        maximizable={true}
        visible={gymMembershipBookingDialog}
        style={{ width: '50vw' }}
        header="GymMembership Bookings"
        modal
        className="p-fluid"
        onHide={() => {
          reset();
          hideDialog();
        }}
      >
        <form onSubmit={handleSubmit(submitForm)} noValidate={true}>
          {/*<input type="hidden" name="id" value={venueBooking?.id} />*/}
          {!membershipBookings.id && (
            <div className="field">
              <label htmlFor="countryId">Membership(Required*)</label>
              <Controller
                name="gymMembershipId"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: 'Membership is required.'
                  }
                }}
                render={({ field, fieldState }) => {
                  return (
                    <>
                      <Dropdown
                        {...field}
                        onChange={(e) => {
                          const gymMembership = gymMemberships.filter((c) => c.id === e.value)[0];
                          setgymMembership(gymMembership);
                          field.onChange(e.value);
                        }}
                        options={gymMemberships.map((c) => ({ id: c.id, name: c.name + '( ' + c.monthsInPlan + ' months )' + '( ' + c.amountInCents / 100 + ' )' }))}
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select membership"
                        filter
                        className="w-full"
                      />
                      {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                    </>
                  );
                }}
              />
            </div>
          )}
          {/* users */}
          {!membershipBookings.id && (
            <div className="field">
              <label htmlFor="userId">Users (Required*)</label>
              <Controller
                name="userId"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: 'User is required.'
                  }
                }}
                render={({ field, fieldState }) => {
                  return (
                    <>
                      <Dropdown
                        {...field}
                        onChange={(e) => {
                          field.onChange(e.value);
                        }}
                        options={users.map((c) => ({
                          id: c.id,
                          name: c.firstName + ' ' + c.lastName + ' (' + c.email + ')'
                        }))}
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select User"
                        filter
                        className="w-full"
                      />
                      {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                    </>
                  );
                }}
              />
            </div>
          )}
          {/* StartDate */}
          <div className="field">
            <label htmlFor="startDate">Start date</label>
            <Controller
              name="startDate"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: 'StartDate is required.'
                }
              }}
              render={({ field, fieldState }) => {
                return (
                  <>
                    <Calendar disabled={membershipBookings.id ? true : false} dateFormat="dd MM yy" value={field.value ? new Date(field.value) : null} onChange={(e) => field.onChange(e.value)} placeholder="Select Date" className="w-full" />
                    {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                  </>
                );
              }}
            />
          </div>
          {/* End date */}
          {membershipBookings.id && (
            <div className="field">
              <label htmlFor="endDate">End date</label>
              <Controller
                name="endDate"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: 'EndDate is required.'
                  }
                }}
                render={({ field, fieldState }) => {
                  return (
                    <>
                      <Calendar minDate={membershipBookings.endDate} dateFormat="dd MM yy" value={field.value ? new Date(field.value) : null} onChange={(e) => field.onChange(e.value)} placeholder="Select Date" className="w-full" />
                      {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                    </>
                  );
                }}
              />
            </div>
          )}

          {/* discount for offline memberships bookings*/}
          {!membershipBookings.id && (
            <div className="field">
              <label htmlFor="discountInCents">Discount</label>
              <Controller
                rules={{
                  validate: (value) => {
                    const membershipPrice = gymMembership?.amountInCents ? gymMembership.amountInCents / 100 : 0;
                    return value < membershipPrice || `Discount must be less than membership Price (₹${gymMembership?.amountInCents ? gymMembership.amountInCents / 100 : 0}) `;
                  }
                }}
                name="discountInCents"
                control={control}
                render={({ field, fieldState }) => {
                  console.log('gymMembership is', gymMembership);
                  return (
                    <>
                      <InputNumber {...field} value={field.value} max={gymMembership?.amountInCents} onChange={(e) => field.onChange(e.value)} placeholder="Enter the value of discount in cents" className="w-full" />
                      {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                    </>
                  );
                }}
              />
            </div>
          )}
          {/* gender */}
          {!membershipBookings.id && (
            <div className="field">
              <label htmlFor="gender">Gender</label>
              <Controller
                name="gender"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: 'Gender is required.'
                  }
                }}
                render={({ field, fieldState }) => {
                  return (
                    <>
                      <Dropdown
                        {...field}
                        onChange={(e) => {
                          field.onChange(e.value);
                        }}
                        options={genderOptions.map((g) => ({
                          id: g.id,
                          name: g.name
                        }))}
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select Gender"
                        filter
                        className="w-full"
                      />
                      {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                    </>
                  );
                }}
              />
            </div>
          )}
          {/* Name of user for the booking */}
          {/* <div className="field">
            <label htmlFor="username">Name of the person</label>
            <Controller
              name="username"
              control={control}
              render={({ field, fieldState }) => {
                return (
                  <>
                    <InputText {...field} value={field.value || ''} placeholder="Enter the name " className="w-full" />
                    {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                  </>
                );
              }}
            />
          </div> */}
          {/* commments */}
          {!membershipBookings.id && (
            <div className="field">
              <label htmlFor="comments">Comments</label>
              <Controller
                name="comments"
                control={control}
                render={({ field, fieldState }) => {
                  return (
                    <>
                      <InputTextarea {...field} value={field.value || ''} placeholder="Enter Comments" className="w-full" />
                      {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                    </>
                  );
                }}
              />
            </div>
          )}
          {/* Toggle booking for yourself or someone else */}
          {/* <div className="field">
            <Controller
              name="self"
              control={control}
              render={({ field, fieldState }) => {
                return (
                  <>
                    <div className="flex justify-start w-4 ">
                      <ToggleButton onLabel="Booking for yourself" offLabel="Booking for someone else" checked={checked} onChange={(e) => setChecked(e.value)} />
                    </div>
                  </>
                );
              }}
            />
          </div> */}
          <Button type="submit" className="w-full" label={`${membershipBookings.id ? 'Update Membership Booking' : 'Create Membership Booking'}`} />
        </form>
      </Dialog>
    </>
  );
};

export default GymMembershipBookingForm;
