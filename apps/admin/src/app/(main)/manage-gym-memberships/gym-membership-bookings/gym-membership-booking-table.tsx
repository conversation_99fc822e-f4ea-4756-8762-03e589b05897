'use client';

import { endOfDay, endOfMonth, format, startOfDay, startOfMonth, subDays } from 'date-fns';
import '@/styles/global.css';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { Column, ColumnFilterElementTemplateOptions } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { Toolbar } from 'primereact/toolbar';
import React from 'react';
import { ChangeEvent, useRef, useState } from 'react';
import GymMembershipBookingForm from './gym-membership-booking-form';
import { api } from '@/src/trpc/react';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { Tag } from 'primereact/tag';
import { formatStatus, getSeverity } from '@/src/lib/utils';
import { GymMembershipBookingStatus } from '@repo/database';
import { Badge } from 'primereact/badge';
import { MultiSelect } from 'primereact/multiselect';

export type IGymMembershipBookingForm = {
  id: string;
  userId: string;
  comments: string | null;
  gymMembershipId: string;
  gender: string;
  startDate: Date;
  endDate: Date;
  self: boolean;
  name: string | null;
  discountInCents: number;
};
export type GymMembershipBooking = {
  id: string;
  startDate: Date;
  endDate: Date;
  gender: string;
  subTotalInCents: number;
  amountInCents: number;
  discountInCents: number;
  status: string;
  gymMembershipId: string;
  gymMembership: {
    id: string;
    name: string;
    monthsInPlan: number;
  };
  userId: string;
  user: {
    email: string;
    mobileNumber: string;
    id: string;
    firstName: string;
    lastName: string | null;
  };
  comments: string | null;
  createdAt: Date;
  updatedAt: Date;
  name: string | null;
  self: boolean;
};
export type IGymMembership = {
  id?: string;
  name: string;
  amountInCents: number;
  monthsInPlan: number;
  discount: number | null;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
};
type IGymMembershipBooking = {
  gymMemberships: IGymMembership[];
  venues: { id: string; name: string; gameVenues: { game: { id: string; name: string } }[] }[];
};
const GymMembershipBookingTable = ({ venues, gymMemberships }: IGymMembershipBooking) => {
  const { data: users } = api.users.getUsers.useQuery();
  const [gymMembershipBookingDialog, setgymMembershipBookingDialog] = useState<boolean>(false);
  const endingOfMonth = endOfMonth(new Date());
  const startingOfMonth = startOfMonth(endingOfMonth);
  const [dates, setDates] = useState<Date[]>([startingOfMonth, endingOfMonth]);
  const [selectedVenueId, setSelectedVenueId] = useState<string | null>(null);

  const { data: getMembershipBookings, isLoading } = api.gymMemberships.getMembershipBookings.useQuery(
    {
      venueId: selectedVenueId,
      startDate: dates[0],
      endDate: dates[1]
    },
    {
      enabled: !!dates[0] && !!dates[1]
    }
  );

  const emptyGymMemberShipBookingForm: IGymMembershipBookingForm = {
    id: '',
    userId: '',
    comments: '',
    gymMembershipId: '',
    gender: '',
    endDate: new Date(),
    startDate: new Date(),
    name: '',
    self: true,
    discountInCents: 0
  };
  const [gymMembershipBooking, setgymMembershipBooking] = useState<IGymMembershipBookingForm>(emptyGymMemberShipBookingForm);
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: [GymMembershipBookingStatus.PAYMENT_COMPLETED, GymMembershipBookingStatus.PAYMENT_OFFLINE], matchMode: FilterMatchMode.IN }
  });
  const [globalFilter, setGlobalFilter] = useState('');
  const dt = useRef<DataTable<any>>(null);

  const onGlobalFilterChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let _filters: any = { ...filters };

    _filters.global.value = value;

    setFilters(_filters);
    setGlobalFilter(value);
  };
  const exportCSV = () => {
    dt.current?.exportCSV();
  };

  const openNew = () => {
    setgymMembershipBooking({ ...emptyGymMemberShipBookingForm });
    setgymMembershipBookingDialog(true);
  };

  const leftToolbarTemplate = () => {
    return (
      <React.Fragment>
        <div className="my-2">
          <Button label="New" icon="pi pi-plus" severity="success" className=" mr-2" onClick={openNew} />
        </div>
      </React.Fragment>
    );
  };

  const rightToolbarTemplate = () => {
    return (
      <React.Fragment>
        {/*<FileUpload mode="basic" accept="image/*" maxFileSize={1000000} chooseLabel="Import" className="mr-2 inline-block" />*/}
        <Button label="Export" icon="pi pi-upload" severity="help" onClick={exportCSV} />
      </React.Fragment>
    );
  };

  const membershipDetailsTemplate = (rowData: GymMembershipBooking) => {
    return (
      <div className="flex flex-column align-items-start gap-2">
        <div className="font-bold">
          {rowData.user.firstName} {rowData.user.lastName}
        </div>
        {rowData.user.mobileNumber && <Badge className="border-round-lg" value={rowData.user.mobileNumber}></Badge>}

        <div> {rowData.user.email}</div>
        <div>
          {format(rowData.startDate, 'd MMM yyyy')} - {format(rowData.endDate, 'd MMM yyyy')}
        </div>
        <div>₹ {rowData.subTotalInCents && rowData.subTotalInCents / 100}</div>
      </div>
    );
  };

  const hideDialog = () => {
    setgymMembershipBookingDialog(false);
  };

  const editGymMembershipBooking = (rowData: GymMembershipBooking) => {
    console.log('rowData is', rowData);
    setgymMembershipBooking({ ...rowData });
    setgymMembershipBookingDialog(true);
  };
  const actionBodyTemplate = (rowData: GymMembershipBooking) => {
    return (
      <>
        <Button icon="pi pi-pencil" rounded severity="success" className="mr-2" onClick={() => editGymMembershipBooking(rowData)} />
      </>
    );
  };

  const handleAmount = (rowData: GymMembershipBooking) => {
    return (
      <>
        <div>₹ {rowData.amountInCents / 100}</div>
      </>
    );
  };

  const handleDiscountAmount = (rowData: GymMembershipBooking) => {
    return (
      <>
        <div>₹ {rowData.discountInCents / 100}</div>
      </>
    );
  };

  const statusBodyTemplate = (rowData: GymMembershipBooking) => {
    console.log('rowData.status', rowData.status);
    return (
      <>
        <Tag value={formatStatus(rowData.status)} severity={getSeverity(rowData.status)} />
      </>
    );
  };

  const statusFilterTemplate = (options: ColumnFilterElementTemplateOptions) => {
    return (
      <>
        <MultiSelect
          value={options.value}
          options={Object.entries(GymMembershipBookingStatus).map((opt) => opt[1])}
          placeholder="Select status"
          className="p-column-filter"
          //   maxSelectedLabels={1}
          onChange={(e) => options.filterCallback(e.value)}
          display="chip"
        />
      </>
    );
  };

  const header = (
    <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
      <h5 className="m-0">Manage GymMembership Bookings</h5>
      <span className="block mt-2 md:mt-0 p-input-icon-left">
        <i className="pi pi-search" />
        <InputText type="search" value={globalFilter} onChange={onGlobalFilterChange} placeholder="Search..." />
      </span>
    </div>
  );
  return (
    <>
      <div className="grid crud-demo">
        <div className="col-12">
          <div className="card">
            <div className="flex flex-row gap-4">
              <div className="flex flex-column">
                <label htmlFor="date">Date</label>
                <Calendar value={dates} onChange={(e) => setDates(e.value as Date[])} selectionMode="range" dateFormat="dd M yy" style={{ width: '300px' }} />
              </div>
              <div className="flex flex-column">
                <label htmlFor="date">Venue</label>
                <Dropdown
                  options={venues}
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select Venue"
                  className="w-full"
                  value={selectedVenueId}
                  onChange={(e) => {
                    setSelectedVenueId(e.target.value);
                  }}
                />
              </div>
            </div>
            <Toolbar className="mb-4" start={leftToolbarTemplate} end={rightToolbarTemplate}></Toolbar>

            <DataTable
              stripedRows
              ref={dt}
              loading={isLoading}
              value={getMembershipBookings}
              dataKey="id"
              paginator
              rows={10}
              filterDisplay="row"
              rowsPerPageOptions={[5, 10, 25]}
              className="datatable-responsive"
              paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
              currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
              filters={filters}
              globalFilterFields={['user.name', 'user.email', 'user.mobileNumber']}
              emptyMessage="No gym Membership bookings found."
              header={header}
              exportFilename="Gym Membership Bookings"
            >
              <Column field="user" header="Membership" body={membershipDetailsTemplate} sortable headerStyle={{ minWidth: '15rem' }}></Column>
              <Column
                header="Status"
                filterField="status"
                showFilterMenu={false}
                filterMenuStyle={{ width: '14rem' }}
                style={{ minWidth: '13rem' }}
                headerStyle={{ minWidth: '5rem' }}
                body={statusBodyTemplate}
                filter
                filterElement={statusFilterTemplate}
              />
              <Column field="amountInCents" header="Amount" sortable body={handleAmount} headerStyle={{ minWidth: '15rem' }}></Column>
              <Column field="coupon" header="Discount" body={handleDiscountAmount} sortable headerStyle={{ minWidth: '15rem' }}></Column>
              <Column header="Actions" body={actionBodyTemplate} frozen alignFrozen="right"></Column>
            </DataTable>

            {users && <GymMembershipBookingForm membershipBookings={gymMembershipBooking} gymMemberships={gymMemberships} users={users} gymMembershipBookingDialog={gymMembershipBookingDialog} hideDialog={hideDialog} />}
          </div>
        </div>
      </div>
    </>
  );
};

export default GymMembershipBookingTable;
