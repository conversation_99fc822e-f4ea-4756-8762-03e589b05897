// 'use server';

import { db } from '@/src/server/db';
import GymMembershipBookingTable from './gym-membership-booking-table';
import { api } from '@/src/trpc/server';

export const dynamic = 'force-dynamic';

const GymMembershipBooking = async () => {
  const gymMemberships = await api.gymMemberships.getGymMemberships();

  const venues = await db.venue.findMany({
    select: {
      id: true,
      name: true,
      gameVenues: {
        select: {
          game: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }
    },
    where: {
      deletedAt: null
    }
  });

  return (
    <>
      <GymMembershipBookingTable venues={venues} gymMemberships={gymMemberships} />
    </>
  );
};

export default GymMembershipBooking;
