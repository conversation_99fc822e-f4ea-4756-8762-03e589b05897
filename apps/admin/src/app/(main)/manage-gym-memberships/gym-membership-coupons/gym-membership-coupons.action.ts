'use server';

import { db } from '@/src/server/db';
import { auth } from '@/src/server/auth';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { IGymMembershipCoupon } from '@/src/_models/gym-membership-coupon.model';
import { ICoupon } from '@/src/_models/coupon.model';

export const createGymMembershipCoupon = async (data: IGymMembershipCoupon) => {
    const session = await auth();

    if (!session) {
        return { error: 'Unauthorized' };
    }

    const { code, active, amount, isPercent, description, expiresAt } = data;

    const FormData = z.object({
        code: z.string().min(1),
        active: z.boolean().optional(),
        amount: z.number(),
        isPercent: z.boolean().optional(),
        description: z.string().min(1),
        expiresAt: z.date()
    });

    const isValidData = FormData.parse({
        code,
        active,
        amount,
        isPercent,
        description,
        expiresAt
    });

    if (!isValidData) {
        return {
            error: 'Invalid data'
        };
    }

    await db.gymMembershipCoupon.create({
        data: {
            code: isValidData.code,
            active: !!isValidData.active,
            amount: isValidData.amount ,
            isPercent: !!isValidData.isPercent,
            description: isValidData.description,
            expiresAt: isValidData.expiresAt
        }
    });

    revalidatePath('/manage-gym-memberships/gym-membership-coupons');
    return {
        message: 'Category created successfully'
    };
};

export const updateGymMembershipCoupon = async (data: IGymMembershipCoupon) => {
    const session = await auth();

    if (!session) {
        return {
            error: 'Unauthorized'
        };
    }
    const { id, code, active, amount, isPercent, description, expiresAt } = data;

    const FormData = z.object({
        id: z.string(),
        code: z.string().min(1),
        active: z.boolean().optional(),
        amount: z.number(),
        isPercent: z.boolean().optional(),
        description: z.string().min(1),
        expiresAt: z.date()
    });

    const isValidData = FormData.parse({
        id,
        code,
        active,
        amount,
        isPercent,
        description,
        expiresAt
    });

    await db.gymMembershipCoupon.update({
        where: {
            id: id as string
        },
        data: {
            code: isValidData.code,
            active: !!isValidData.active,
            amount: isValidData.amount ,
            isPercent: !!isValidData.isPercent,
            description: isValidData.description,
            expiresAt: isValidData.expiresAt
        }
    });

    revalidatePath('/manage-gym-memberships/gym-membership-coupons');
    return {
        message: 'coupon updated successfully'
    };
};
