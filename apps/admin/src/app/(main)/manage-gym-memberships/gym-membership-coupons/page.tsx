// 'use server';
import { db } from '@/src/server/db';
import { Skeleton } from 'primereact/skeleton';
import { Suspense } from 'react';
import GymMembershipCouponsTable from './gym-membership-coupons-table';

export const dynamic = 'force-dynamic';

const GymMembershipCoupons = async () => {
  const gymMembershipCoupons = await db.gymMembershipCoupon.findMany({
    select: {
      id: true,
      code: true,
      amount: true,
      isPercent: true,
      description: true,
      expiresAt: true,
      active: true
    }
  });

  return (
    <Suspense fallback={<Skeleton width="100%" height="100px" />}>
      <GymMembershipCouponsTable coupons={gymMembershipCoupons} />
    </Suspense>
  );
};

export default GymMembershipCoupons;
