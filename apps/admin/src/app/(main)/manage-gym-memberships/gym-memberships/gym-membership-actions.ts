'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { ICategory } from '@/src/_models/category.model';
import { auth } from '@/src/server/auth';
import { IBlogCategory } from '@/src/_models/blog-category.model';
import { IGymMembershipForm } from '@/src/_models/gym-membership.model';

export const createGymMembership = async (data: IGymMembershipForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { name, active, amountInCents, discount, monthsInPlan, venueId, features } = data;

  const FormData = z.object({
    name: z.string().min(1),
    active: z.boolean().optional(),
    amountInCents: z.number(),
    discount: z.number().nullable().optional(),
    monthsInPlan: z.number(),
    venueId: z.string().optional(),
    features: z.array(z.object({ name: z.string() }))
  });

  const isValidData = FormData.parse({
    name,
    active,
    amountInCents,
    discount,
    monthsInPlan,
    features,
    venueId
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  await db.gymMembership.create({
    data: {
      name: isValidData.name,
      active: !!isValidData.active,
      amountInCents: isValidData.amountInCents * 100,
      discount: isValidData.discount ? isValidData.discount * 100 : 0,
      monthsInPlan: isValidData.monthsInPlan,
      features: isValidData.features,
      venueId: isValidData.venueId
    }
  });

  revalidatePath('/manage-gym-memberships/gym-memberships');
  revalidatePath('manage-gym-memberships/gym-membership-bookings');
  revalidatePath('/manage-gym-memberships/reports');
  return {
    message: 'Gym Membership created successfully'
  };
};

export const updateGymMembership = async (data: IGymMembershipForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, name, active, amountInCents, discount, monthsInPlan, features, venueId } = data;

  const FormData = z.object({
    id: z.string(),
    name: z.string().min(1),
    active: z.boolean().optional(),
    amountInCents: z.number(),
    discount: z.number().nullable().optional(),
    monthsInPlan: z.number(),
    venueId: z.string().optional(),
    features: z.array(z.object({ name: z.string() }))
  });

  const isValidData = FormData.parse({
    id,
    name,
    active,
    amountInCents,
    discount,
    monthsInPlan,
    features,
    venueId
  });

  await db.gymMembership.update({
    where: {
      id: isValidData.id
    },
    data: {
      name: isValidData.name,
      active: !!isValidData.active,
      amountInCents: isValidData.amountInCents * 100,
      discount: isValidData.discount ? isValidData.discount * 100 : 0,
      monthsInPlan: isValidData.monthsInPlan,
      features: isValidData.features,
      venueId: isValidData.venueId
    }
  });

  revalidatePath('/manage-gym-memberships/gym-memberships');
  revalidatePath('/manage-gym-memberships/reports');
  return {
    message: 'Gym Membership updated successfully'
  };
};

export const deleteGymMembership = async (id: string) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  await db.gymMembership.update({
    data: {
      deletedAt: new Date()
    },
    where: {
      id: id
    }
  });

  revalidatePath('/manage-gym-memberships/gym-memberships');
  revalidatePath('/manage-gym-memberships/reports');
  return {
    message: 'Gym Membership deleted successfully'
  };
};
