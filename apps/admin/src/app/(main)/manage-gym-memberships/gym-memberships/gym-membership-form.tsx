import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { InputText } from 'primereact/inputtext';
import { classNames } from 'primereact/utils';
import { Checkbox } from 'primereact/checkbox';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import SubmitButton from '@/src/_components/shared/submit-button';
import React, { useEffect } from 'react';
import { createGymMembership, updateGymMembership } from '@/src/app/(main)/manage-gym-memberships/gym-memberships/gym-membership-actions';
import useToastContext from '@/src/_hooks/useToast';
import { slugifyName } from '@/src/lib/utils';
import { IBlogCategory, IBlogCategoryForm } from '@/src/_models/blog-category.model';
import { IGymMembershipForm } from '@/src/_models/gym-membership.model';
import { InputNumber } from 'primereact/inputnumber';

type GymMembershipFormProps = {
  venues: { id: string; name: string }[];
  gymMembership: IGymMembershipForm;
  hideDialog: () => void;
};

const GymMembershipForm = ({ venues, gymMembership, hideDialog }: GymMembershipFormProps) => {
  console.log('gym membership data is', gymMembership);
  const { showToast } = useToastContext();
  const {
    control,
    setValue,
    handleSubmit,
    reset,
    watch,
    formState: { isLoading }
  } = useForm<IGymMembershipForm>({
    defaultValues: {
      ...gymMembership,
      features: gymMembership.features
    }
  });

  const {
    fields: featuresFields,
    append: featuresAppend,
    remove: featuresRemove
  } = useFieldArray({
    control,
    name: 'features'
  });

  useEffect(() => {
    reset({ ...gymMembership, amountInCents: gymMembership.amountInCents / 100, discount: gymMembership.discount && gymMembership.discount / 100 });
  }, [gymMembership]);

  const callServerAction = (data: IGymMembershipForm) => {
    if (gymMembership?.id) {
      return updateGymMembership(data);
    } else {
      return createGymMembership(data);
    }
  };

  const submitForm = (data: IGymMembershipForm) => {
    return callServerAction(data)
      .then((resp) => {
        if (resp.error) {
          showToast('error', 'Error', resp.error);
        }
        if (resp.message) {
          showToast('success', 'Successful', resp.message);
          hideDialog();
        }
      })
      .catch((err) => {
        showToast('error', 'Error', err.message);
        // console.log(err);
      })
      .finally(() => {});
  };

  return (
    <>
      <form onSubmit={handleSubmit(submitForm)} noValidate={true}>
        <div className="field">
          <label htmlFor="name">Name</label>
          <Controller
            name="name"
            control={control}
            rules={{
              required: {
                value: true,
                message: 'Name is required.'
              }
            }}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputText
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>

        <div className="field">
          <label htmlFor="slug">Number of Months</label>
          <Controller
            name="monthsInPlan"
            control={control}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputNumber
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    value={field.value}
                    onChange={(e) => field.onChange(e.value)}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>

        <div className="field">
          <label htmlFor="venueId">Venue</label>
          <Controller
            name="venueId"
            control={control}
            render={({ field, fieldState }) => {
              console.log('field value is', field.value);
              return (
                <>
                  <Dropdown
                    options={venues.map((venue) => ({
                      value: venue.id,
                      label: venue.name
                    }))}
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    value={gymMembership.venueId || ''}
                    onChange={field.onChange}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>

        <div className="field">
          <label htmlFor="slug">Amount</label>
          <Controller
            name="amountInCents"
            control={control}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputNumber
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    value={field.value}
                    onChange={(e) => field.onChange(e.value)}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>

        <div className="field">
          <label htmlFor="discount">Discount</label>
          <Controller
            name="discount"
            control={control}
            rules={{
              max: {
                value: 100,
                message: 'The maximum value for discount can be 100%'
              }
            }}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputNumber
                    placeholder="Considers the value that you enter in percentage"
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    value={field.value}
                    onChange={(e) => field.onChange(e.value)}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>

        <div className="field">
          <Controller
            name="active"
            control={control}
            render={({ field }) => {
              return (
                <div className="flex gap-2">
                  <Checkbox checked={field.value} {...field} />
                  <label htmlFor="active">Active</label>
                </div>
              );
            }}
          />
        </div>
        <div className="flex justify-content-between mb-4">
          <h4>Features</h4>
          <Button type="button" icon="pi pi-plus" onClick={() => featuresAppend({ name: '' })} />
        </div>
        {featuresFields.map((field, index) => (
          <div key={`feature-${index}`} className="mb-2">
            <Controller
              name={`features.${index}.name`}
              control={control}
              rules={{
                required: {
                  value: true,
                  message: 'Feature is required.'
                }
              }}
              render={({ field, fieldState }) => {
                return (
                  <>
                    <div className="p-inputgroup flex-1">
                      <InputText
                        placeholder={`Features ` + (index + 1)}
                        className={classNames({
                          'p-invalid': fieldState.error
                        })}
                        {...field}
                      />
                      <Button className="p-inputgroup-addon" icon="pi pi-trash" severity="danger" type="button" onClick={() => featuresRemove(index)} />
                    </div>
                    {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                  </>
                );
              }}
            />
          </div>
        ))}
        <div className="flex flex-row gap-4">
          <Button label="Cancel" type="reset" icon="pi pi-times" severity="danger" onClick={hideDialog} />
          <SubmitButton label="Save" icon="pi pi-check" />
        </div>
      </form>
    </>
  );
};

export default GymMembershipForm;
