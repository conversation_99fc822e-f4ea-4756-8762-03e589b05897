'use client';
import { <PERSON><PERSON> } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Toolbar } from 'primereact/toolbar';
import { confirmDialog } from 'primereact/confirmdialog';
import React, { ChangeEvent, useRef, useState } from 'react';
import { Tag } from 'primereact/tag';
import GymMembershipForm from '@/src/app/(main)/manage-gym-memberships/gym-memberships/gym-membership-form';
import { FilterMatchMode } from 'primereact/api';
import { IGymMembership, IGymMembershipForm } from '@/src/_models/gym-membership.model';
import { format } from 'date-fns';
import useToastContext from '@/src/_hooks/useToast';
import { deleteGymMembership } from '@/src/app/(main)/manage-gym-memberships/gym-memberships/gym-membership-actions';

const GymMembershipsTable = ({ venues, gymMemberships }: { gymMemberships: IGymMembership[]; venues: { id: string; name: string }[] }) => {
  const { showToast } = useToastContext();
  const emptyGymMembership: IGymMembershipForm = { id: '', name: '', amountInCents: 0, monthsInPlan: 1, discount: null, active: true, features: [], venueId: '' };
  const [gymMembershipDialog, setGymMembershipDialog] = useState(false);
  const [gymMembership, setGymMembership] = useState<IGymMembershipForm>(emptyGymMembership);
  const [selectedCategories, setSelectedCategories] = useState(null);
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS }
  });
  const [globalFilter, setGlobalFilter] = useState('');
  const dt = useRef<DataTable<any>>(null);

  const openNew = () => {
    setGymMembership({ ...emptyGymMembership });
    setGymMembershipDialog(true);
  };

  const hideDialog = () => {
    setGymMembershipDialog(false);
  };

  const editGymMembership = (gymMembership: IGymMembershipForm) => {
    console.log('details aree', gymMembership);
    setGymMembership({ ...gymMembership });
    setGymMembershipDialog(true);
  };

  const exportCSV = () => {
    dt.current?.exportCSV();
  };

  const leftToolbarTemplate = () => {
    return (
      <React.Fragment>
        <div className="my-2">
          <Button label="New" icon="pi pi-plus" severity="success" className=" mr-2" onClick={openNew} />
        </div>
      </React.Fragment>
    );
  };

  const rightToolbarTemplate = () => {
    return (
      <React.Fragment>
        {/*<FileUpload mode="basic" accept="image/*" maxFileSize={1000000} chooseLabel="Import" className="mr-2 inline-block" />*/}
        <Button label="Export" icon="pi pi-upload" severity="help" onClick={exportCSV} />
      </React.Fragment>
    );
  };

  const idBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Id</span>
        {rowData.id}
      </>
    );
  };

  const nameBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Name</span>
        {rowData.name}
      </>
    );
  };

  const venueBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Venue</span>
        {rowData?.venue?.name}
      </>
    );
  };

  const amountBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Amount</span>₹{rowData.amountInCents / 100.0}
      </>
    );
  };

  const discountBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Discount</span>
        {rowData.discount && rowData.discount / 100}%
      </>
    );
  };

  const createdAtBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Created At</span>
        {rowData.createdAt && format(rowData.createdAt, 'd MMM yyyy hh:mm aaa')}
      </>
    );
  };

  const updatedAtBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Updated At</span>
        {rowData.updatedAt && format(rowData.updatedAt, 'd MMM yyyy hh:mm aaa')}
      </>
    );
  };

  const deleteGymMembershipConfirmation = async (id: string) => {
    confirmDialog({
      message: 'Are you sure you want to proceed?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        deleteGymMembership(id).then((res) => {
          if (res.error) {
            showToast('error', 'Error', res.error);
          } else {
            showToast('success', 'Success', 'Gym membership deleted successfully');
          }
        });
      },
      reject: () => {
        console.log('Rejected');
      }
    });
  };

  const actionBodyTemplate = (rowData: typeof emptyGymMembership) => {
    return (
      <>
        <Button icon="pi pi-pencil" rounded severity="success" className="mr-2" onClick={() => editGymMembership(rowData)} />
        <Button icon="pi pi-trash" rounded severity="danger" className="mr-2" onClick={() => deleteGymMembershipConfirmation(rowData.id!)} />
      </>
    );
  };

  const onGlobalFilterChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let _filters: any = { ...filters };

    _filters.global.value = value;

    setFilters(_filters);
    setGlobalFilter(value);
  };

  const header = (
    <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
      <h5 className="m-0">Manage Gym Memberships</h5>
      <span className="block mt-2 md:mt-0 p-input-icon-left">
        <i className="pi pi-search" />
        <InputText type="search" value={globalFilter} onChange={onGlobalFilterChange} placeholder="Search..." />
      </span>
    </div>
  );

  const activeBodyTemplate = (rowData: IGymMembership) => {
    return (
      <>
        <span className="p-column-title">Active</span>
        {rowData.active ? <Tag severity="success">Yes</Tag> : <Tag severity="danger">No</Tag>}
      </>
    );
  };

  return (
    <div className="grid crud-demo">
      <div className="col-12">
        <div className="card">
          <Toolbar className="mb-4" start={leftToolbarTemplate} end={rightToolbarTemplate}></Toolbar>

          <DataTable
            stripedRows
            ref={dt}
            value={gymMemberships}
            selection={selectedCategories}
            onSelectionChange={(e) => setSelectedCategories(e.value as any)}
            dataKey="id"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25]}
            className="datatable-responsive"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            filters={filters}
            globalFilterFields={['id', 'name']}
            emptyMessage="No gym memberships found."
            header={header}
            exportFilename="Gym Memberships"
          >
            {/* <Column field="id" header="Id" sortable body={idBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column> */}
            <Column field="name" header="Name" sortable body={nameBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="venue" header="Venue" sortable body={venueBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="amount" header="Amount" sortable body={amountBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="discount" header="Discount" sortable body={discountBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="active" header="Active" sortable body={activeBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            {/* <Column field="createdAt" header="Created At" sortable body={createdAtBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column> */}
            {/* <Column field="updatedAt" header="Updated At" sortable body={updatedAtBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column> */}
            <Column body={actionBodyTemplate} headerStyle={{ minWidth: '10rem' }} frozen={true}></Column>
          </DataTable>

          <Dialog visible={gymMembershipDialog} style={{ width: '50vw' }} header="Gym Membership Details" modal className="p-fluid" onHide={hideDialog}>
            <GymMembershipForm venues={venues} gymMembership={gymMembership} hideDialog={hideDialog} />
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default GymMembershipsTable;
