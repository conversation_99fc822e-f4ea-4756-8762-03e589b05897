// 'use server';

import { db } from '@/src/server/db';
import GymMembershipsTable from '@/src/app/(main)/manage-gym-memberships/gym-memberships/gym-memberships-table';

export const dynamic = 'force-dynamic';

const GymMembershipsPage = async () => {
  const venuesHasGym = await db.venue.findMany({
    select: {
      id: true,
      name: true
    },
    orderBy: {
      name: 'asc'
    },
    where: {
      hasGym: true,
      deletedAt: null
    }
  });
  const gymMemberships = await db.gymMembership.findMany({
    select: {
      id: true,
      name: true,
      venue: {
        select: {
          id: true,
          name: true
        }
      },
      venueId: true,
      amountInCents: true,
      monthsInPlan: true,
      discount: true,
      active: true,
      features: true,
      createdAt: true,
      updatedAt: true
    },
    where: {
      deletedAt: null
    }
  });
  // @ts-ignore
  return <GymMembershipsTable venues={venuesHasGym} gymMemberships={gymMemberships.map((gymMembership) => ({ ...gymMembership, features: gymMembership.features as { name: string }[] }))} />;
};

export default GymMembershipsPage;
