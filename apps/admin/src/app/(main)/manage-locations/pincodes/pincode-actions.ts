'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { auth } from '@/src/server/auth';
import { IStateForm } from '@/src/_models/state.model';
import { IPincodeForm } from '@/src/_models/pincode.model';

export const createPincode = async (data: IPincodeForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { pincode, stateId, city } = data;

  const FormData = z.object({
    pincode: z.string().min(1),
    city: z.string().optional(),
    stateId: z.string().nullable()
  });

  const isValidData = FormData.parse({
    pincode,
    stateId,
    city
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  await db.availablePincodes.create({
    data: {
      pincode: isValidData.pincode,
      stateId: isValidData.stateId,
      city: isValidData.city
    }
  });

  // revalidatePath('/manage-locations/countries');
  revalidatePath('/manage-locations/pincodes');
  return {
    message: 'Pincode created successfully'
  };
};

export const updatePincode = async (data: IPincodeForm) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, pincode, stateId, city } = data;

  const FormData = z.object({
    id: z.string().min(1),
    pincode: z.string().min(1),
    stateId: z.string().nullable(),
    city: z.string().optional()
  });

  const isValidData = FormData.parse({
    id,
    pincode,
    stateId,
    city
  });

  await db.availablePincodes.update({
    where: {
      id: id as string
    },
    data: {
      pincode: isValidData.pincode,
      stateId: isValidData.stateId,
      city: isValidData.city
    }
  });

  // revalidatePath('/manage-locations/countries');
  revalidatePath('/manage-locations/pincodes');
  return {
    message: 'Pincode updated successfully'
  };
};

export const deletePincode = async (ids: string[]) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  await db.availablePincodes.updateMany({
    where: {
      id: {
        in: ids
      }
    },
    data: {
      deletedAt: new Date()
    }
  });
};
