'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { auth } from '@/src/server/auth';
import { IBrand } from '@/src/_models/brand.model';

const checkSlugNotUnique = async (slug: string, id: string | null = null) => {
  if (id) {
    const product = await db.product.findUnique({
      select: {
        id: true
      },
      where: {
        NOT: {
          id: id
        },
        slug
      }
    });

    return !!product;
  }

  const product = await db.product.findUnique({
    where: {
      slug
    }
  });

  return !!product;
};

export const createBrand = async (data: IBrand) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { name, slug, seoTitle, seoDescription, seoKeywords, mediaId } = data;

  const FormData = z.object({
    name: z.string().min(1),
    slug: z.string().min(1),
    seoTitle: z.string().nullable(),
    seoDescription: z.string().nullable(),
    seoKeywords: z.array(z.string()).nullable(),
    mediaId: z.string().nullable()
  });

  const isValidData = FormData.parse({
    name,
    slug,
    seoTitle,
    seoDescription,
    seoKeywords,
    mediaId
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  if (await checkSlugNotUnique(slug, null)) {
    return {
      error: 'Slug already exists'
    };
  }

  await db.brand.create({
    data: {
      name,
      slug,
      seoTitle,
      seoDescription,
      seoKeywords,
      mediaId
    }
  });

  revalidatePath('/manage-products/brands');
  revalidatePath('/manage-products/products');
  return {
    message: 'Brand created successfully'
  };
};

export const updateBrand = async (data: IBrand) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, name, slug, seoTitle, seoDescription, seoKeywords, mediaId } = data;

  const FormData = z.object({
    id: z.string().min(1),
    name: z.string().min(1),
    slug: z.string().min(1),
    seoTitle: z.string().nullable(),
    seoDescription: z.string().nullable(),
    seoKeywords: z.array(z.string()).nullable(),
    mediaId: z.string().nullable()
  });

  const isValidData = FormData.parse({
    id,
    name,
    slug,
    seoTitle,
    seoDescription,
    seoKeywords,
    mediaId
  });

  if (await checkSlugNotUnique(slug, id)) {
    return {
      error: 'Slug already exists'
    };
  }

  await db.brand.update({
    where: {
      id: id as string
    },
    data: {
      name,
      slug,
      seoTitle,
      seoDescription,
      seoKeywords,
      mediaId
    }
  });

  revalidatePath('/manage-products/brands');
  revalidatePath('/manage-products/products');
  return {
    message: 'Brand updated successfully'
  };
};

export const deleteBrand = async (brandId: string) => {
  const brandWithProducts = await db.brand.findUnique({
    where: { id: brandId },
    include: {
      Product: {
        where: {
          deletedAt: null
        }
      }
    }
  });
  if (brandWithProducts?.Product.length != 0) {
    return {
      error: 'Brand cannot be deleted as there are associated products'
    };
  }
  try {
    {
      brandWithProducts?.Product.length == 0 &&
        (await db.brand.update({
          where: {
            id: brandId
          },
          data: {
            deletedAt: new Date()
          }
        }));
    }
    revalidatePath('/manage-products/brands');
    revalidatePath('/manage-products/products');
    return {
      message: 'Brand deleted successfully'
    };
  } catch (err) {
    return {
      error: 'Error in deleting'
    };
  }
};
