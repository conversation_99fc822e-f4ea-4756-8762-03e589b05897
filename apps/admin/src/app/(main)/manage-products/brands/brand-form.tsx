import { Controller, useForm } from 'react-hook-form';
import { InputText } from 'primereact/inputtext';
import { classNames } from 'primereact/utils';
import { But<PERSON> } from 'primereact/button';
import SubmitButton from '@/src/_components/shared/submit-button';
import React, { useEffect, useRef, useState } from 'react';
import { createBrand, updateBrand } from '@/src/app/(main)/manage-products/brands/brand-actions';
import useToastContext from '@/src/_hooks/useToast';
import { IBrand } from '@/src/_models/brand.model';
import { InputTextarea } from 'primereact/inputtextarea';
import { Chips } from 'primereact/chips';
import { FileUpload, FileUploadSelectEvent } from 'primereact/fileupload';
import uploadProductImage from '@/src/app/(main)/upload-image-actions';
import { Image } from 'primereact/image';
import { slugifyName } from '@/src/lib/utils';

type CurrencyFormProps = {
  brand: IBrand;
  hideDialog: () => void;
};

const BrandForm = ({ brand, hideDialog }: CurrencyFormProps) => {
  const fileInputRef = useRef<FileUpload>(null);
  const [uploadingState, setUploadingState] = useState<0 | 1 | 2>(0);
  const [imageUrl, setImageUrl] = useState<string>();
  const { showToast } = useToastContext();
  const {
    control,
    setValue,
    handleSubmit,
    reset,
    watch,
    getValues,
    formState: { isLoading }
  } = useForm<IBrand>({
    defaultValues: brand
  });

  useEffect(() => {
    reset({ ...brand });
  }, [brand]);

  const callServerAction = (data: IBrand) => {
    if (brand?.id) {
      return updateBrand(data);
    } else {
      return createBrand(data);
    }
  };

  const submitForm = (data: IBrand) => {
    return callServerAction(data)
      .then((resp) => {
        if (resp.error) {
          showToast('error', 'Error', resp.error);
        }
        if (resp.message) {
          showToast('success', 'Successful', resp.message);
          hideDialog();
        }
      })
      .catch((err) => {
        showToast('error', 'Error', err.message);
        // console.log(err);
      })
      .finally(() => {});
  };

  const onSelectImage = async (event: FileUploadSelectEvent) => {
    if (event.files.length > 0) {
      event.files.forEach((image) => {
        setUploadingState(1);
        uploadProductImage(image.name, image.type)
          .then(async (resp) => {
            const { id, fileUrl, presignedUrl } = resp;
            const requestOptions = {
              method: 'PUT',
              body: image
            };
            const res = await fetch(presignedUrl!, requestOptions);
            // console.log(res, presignedUrl, id);
            if (res.ok) {
              setUploadingState(0);
              setValue('mediaId', id!);
              setImageUrl(fileUrl);
              fileInputRef.current?.clear();
            }
          })
          .catch(() => {
            setUploadingState(2);
            fileInputRef.current?.clear();
          });
      });
    }
  };


  const generateSlug=()=>{
    const name=getValues('name');
    setValue("slug",slugifyName(name));
  }
  return (
    <>
      <form onSubmit={handleSubmit(submitForm)} noValidate={true}>
        <input type="hidden" name="id" value={brand?.id!} />
        {brand.media && brand.media.fileUrl && !imageUrl && <Image src={brand.media.fileUrl} alt="Image" className="relative" height="100" width="auto" preview />}
        {imageUrl && uploadingState === 0 && <Image src={imageUrl} alt="Image" className="relative" height="100" width="auto" preview />}
        <div className="field mb-2">
          <FileUpload ref={fileInputRef} mode="basic" accept="image/*" maxFileSize={1000000} onSelect={onSelectImage} />
        </div>
        {uploadingState === 1 && <div className="text-blue-500">Image uploading.</div>}
        {uploadingState === 2 && <div className="text-red-500">Image upload failed.</div>}
        <div className="field">
          <label htmlFor="name">Name (Required*)</label>
          <Controller
            name="name"
            control={control}
            rules={{
              required: {
                value: true,
                message: 'Name is required.'
              }
            }}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputText
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>
        <div className="field">
          <label htmlFor="slug">Slug (Required*)
          &nbsp;
            <a className="cursor-pointer" onClick={() => generateSlug()}>
              Generate Slug
            </a>
          </label>
          <Controller
            name="slug"
            control={control}
            rules={{
              required: {
                value: true,
                message: 'Slug is required.'
              }
            }}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputText
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>
        <div className="field">
          <label htmlFor="seoTitle">SEO Title</label>
          <Controller
            name="seoTitle"
            control={control}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputText
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    value={field.value || ''}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>
        <div className="field">
          <label htmlFor="seoDescription">SEO Description</label>
          <Controller
            name="seoDescription"
            control={control}
            render={({ field, fieldState }) => {
              return (
                <>
                  <InputTextarea
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    value={field.value || ''}
                    rows={5}
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>
        <div className="field">
          <label htmlFor="seoKeywords">SEO Keywords</label>
          <Controller
            name="seoKeywords"
            control={control}
            render={({ field, fieldState }) => {
              return (
                <>
                  <Chips
                    className={classNames({
                      'p-invalid': fieldState.error
                    })}
                    {...field}
                    value={field.value || []}
                    separator=","
                  />
                  {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
                </>
              );
            }}
          />
        </div>
        <div className="field"></div>
        <div className="flex flex-row gap-4">
          <Button label="Cancel" type="reset" icon="pi pi-times" severity="danger" onClick={hideDialog} />
          <SubmitButton label="Save" icon="pi pi-check" />
        </div>
      </form>
    </>
  );
};

export default BrandForm;
