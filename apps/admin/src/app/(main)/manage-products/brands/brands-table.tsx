'use client';
import { <PERSON><PERSON> } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Toolbar } from 'primereact/toolbar';
import React, { ChangeEvent, useRef, useState } from 'react';
import { Demo } from '@/types';
import BrandForm from '@/src/app/(main)/manage-products/brands/brand-form';
import { IBrand } from '@/src/_models/brand.model';
import { IMedia } from '@/src/_models/media.model';
import { Image } from 'primereact/image';
import { FilterMatchMode } from 'primereact/api';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import useToastContext from '@/src/_hooks/useToast';
import { deleteBrand } from './brand-actions';

const BrandsTable = ({ brands }: { brands: IBrand[] }) => {
  const emptyBrand: IBrand = { id: '', name: '', slug: '', seoTitle: '', seoDescription: '', seoKeywords: [], mediaId: null, media: null };
  const [brandDialog, setBrandDialog] = useState(false);
  const [brand, setBrand] = useState<IBrand>(emptyBrand);
  const { showToast } = useToastContext();
  const [selectedCategories, setSelectedCategories] = useState(null);
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS }
  });
  const [globalFilter, setGlobalFilter] = useState('');
  const dt = useRef<DataTable<any>>(null);

  const openNew = () => {
    setBrand({ ...emptyBrand });
    setBrandDialog(true);
  };

  const hideDialog = () => {
    setBrandDialog(false);
  };

  const editBrand = (brand: IBrand) => {
    setBrand({ ...brand });
    setBrandDialog(true);
  };

  // const deleteRecord=(brand:IBrand)
  // {

  // }

  const exportCSV = () => {
    dt.current?.exportCSV();
  };

  const leftToolbarTemplate = () => {
    return (
      <React.Fragment>
        <div className="my-2">
          <Button label="New" icon="pi pi-plus" severity="success" className=" mr-2" onClick={openNew} />
        </div>
      </React.Fragment>
    );
  };

  const rightToolbarTemplate = () => {
    return (
      <React.Fragment>
        {/*<FileUpload mode="basic" accept="image/*" maxFileSize={1000000} chooseLabel="Import" className="mr-2 inline-block" />*/}
        <Button label="Export" icon="pi pi-upload" severity="help" onClick={exportCSV} />
      </React.Fragment>
    );
  };

  const idBodyTemplate = (rowData: Demo.Product) => {
    return (
      <>
        <span className="p-column-title">Id</span>
        {rowData.id}
      </>
    );
  };

  const nameBodyTemplate = (rowData: Demo.Product) => {
    return (
      <>
        <span className="p-column-title">Name</span>
        {rowData.name}
      </>
    );
  };
  const reject = () => {
    console.log('rejected your request');
  };
  const accept = (brandId:string) => {
    return deleteBrand(brandId)
      .then((resp) => {
        if (resp.message) {
          showToast('success', 'Successful', resp.message);
        }
        if (resp.error) {
          showToast('error', 'Error', resp.error);
        }
      })
      .catch((err) => {
        showToast('error', 'Error', err.message);
      });
  };

const deleteRecord=(brand:IBrand)=>{
  confirmDialog({
    message: 'Do you want to delete this record?',
    header: 'Delete Confirmation',
    icon: 'pi pi-info-circle',
    acceptClassName: 'p-button-danger',
    accept: () => accept(brand.id!),
    reject
  });
}
  const actionBodyTemplate = (rowData: typeof emptyBrand) => {
    return (
      <>
        <Button icon="pi pi-pencil" rounded severity="success" className="mr-2" onClick={() => editBrand(rowData)} />
          <Button icon="pi pi-trash" rounded severity="danger"  className="mr-2" onClick={()=>deleteRecord(rowData)}></Button>
      </>
    );
  };

  const onGlobalFilterChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let _filters: any = { ...filters };

    _filters.global.value = value;

    setFilters(_filters);
    setGlobalFilter(value);
  };

  const header = (
    <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
      <h5 className="m-0">Manage Brands</h5>
      <span className="block mt-2 md:mt-0 p-input-icon-left">
        <i className="pi pi-search" />
        <InputText type="search" value={globalFilter} onChange={onGlobalFilterChange} placeholder="Search..." />
      </span>
    </div>
  );

  const fileKeyBodyTemplate = (rowData: IBrand) => {
    if (!rowData.media?.fileUrl) {
      return;
    }
    return (
      <div className="flex justify-content-center relative">
        <Image src={rowData.media?.fileUrl} alt="Image" className="relative" width="100" height="auto" preview />
      </div>
    );
  };

  return (
    <div className="grid crud-demo">
      <div className="col-12">
        <div className="card">
          <Toolbar className="mb-4" start={leftToolbarTemplate} end={rightToolbarTemplate}></Toolbar>

          <DataTable
            stripedRows
            ref={dt}
            value={brands}
            onSelectionChange={(e) => setSelectedCategories(e.value as any)}
            dataKey="id"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25]}
            className="datatable-responsive"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            filters={filters}
            globalFilterFields={['id', 'name', 'slug']}
            emptyMessage="No brands found."
            header={header}
            exportFilename="Brands"
          >
            <Column field="id" header="Id" sortable body={idBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="name" header="Name" sortable body={nameBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="slug" header="Slug" sortable headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="media" header="Media" sortable body={fileKeyBodyTemplate} headerStyle={{ minWidth: '15rem' }}></Column>
            <Column body={actionBodyTemplate} headerStyle={{ minWidth: '10rem' }} frozen={true}></Column>
          </DataTable>

          <Dialog visible={brandDialog} style={{ width: '50vw' }} header="Brand Details" modal className="p-fluid" onHide={hideDialog}>
            <BrandForm brand={brand} hideDialog={hideDialog} />
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default BrandsTable;
