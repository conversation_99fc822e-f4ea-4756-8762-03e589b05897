// 'use server';
import React, { Suspense } from 'react';
import { db } from '@/src/server/db';
import BrandsTable from './brands-table';
import { Skeleton } from 'primereact/skeleton';

export const dynamic = 'force-dynamic';

const Brands = async () => {
  const brands = await db.brand.findMany({
    select: {
      id: true,
      name: true,
      slug: true,
      seoTitle: true,
      seoDescription: true,
      seoKeywords: true,
      mediaId: true,
      media: {
        select: {
          id: true,
          fileUrl: true
        }
      }
    },
    where: {
      deletedAt: null
    },
    orderBy: {
      name: 'asc'
    }
  });

  return (
    <Suspense fallback={<Skeleton width="100%" height="100px" />}>
      <BrandsTable brands={brands} />
    </Suspense>
  );
};

export default Brands;
