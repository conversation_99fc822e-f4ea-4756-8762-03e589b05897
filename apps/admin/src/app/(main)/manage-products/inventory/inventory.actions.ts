'use server';
import { db } from '@/src/server/db';
import { auth } from '@/src/server/auth';
import { z } from 'zod';
import { ProductVariantInventoryUpdateType } from '@repo/database';
import { revalidatePath } from 'next/cache';
import { IInventoryUpdate } from './inventory-form-available';

export const createProductVariantInventory = async (data: IInventoryUpdate, productVariantId: string) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const adminUser = await db.adminUser.findFirst({
    where: {
      email: session?.user?.email!
    }
  });
  const { quantity, reason, updateType } = data;

  const FormData = z.object({
    quantity: z.string(),
    reason: z.string(),
    updateType: z.enum([ProductVariantInventoryUpdateType.ADJUST_AVAILABLE, ProductVariantInventoryUpdateType.MOVE_TO_UNAVAILABLE])
  });

  const isValidData = FormData.parse({
    quantity,
    reason,
    updateType
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  let productVariantInv = await db.productVariantInventory.findUnique({
    where: {
      id: data.productVariantInventoryId
    }
  });

  console.log('productVariantInv issssss', productVariantInv);

  if (!productVariantInv) {
    productVariantInv = await db.productVariantInventory.create({
      data: {
        productVariantId: productVariantId
      }
    });
  }

  if (data.updateType === ProductVariantInventoryUpdateType.ADJUST_AVAILABLE) {
    await db.productVariantInventory.update({
      data: {
        available: {
          increment: Number(data.quantity)
        },
        onHand: {
          increment: Number(data.quantity)
        }
      },
      where: {
        id: productVariantInv?.id
      }
    });
  } else if (data.updateType === ProductVariantInventoryUpdateType.MOVE_TO_UNAVAILABLE && Number(productVariantInv.available) >= Number(data.quantity)) {
    await db.productVariantInventory.update({
      data: {
        unavailable: {
          increment: Number(data.quantity)
        },
        available: {
          decrement: Number(data.quantity)
        }
      },
      where: {
        id: productVariantInv?.id
      }
    });
  } else {
    return {
      error: 'Cannot move items to unavialable as we do not have that much quantity available'
    };
  }

  try {
    await db.productVariantInventoryUpdate.create({
      data: {
        quantity: parseInt(isValidData.quantity),
        reason: isValidData.reason,
        updateById: adminUser?.id!,
        productVariantInventoryId: productVariantInv.id,
        updateType: isValidData.updateType
      }
    });
    revalidatePath('/manage-products/inventory');
    return {
      message: 'Inventory updated successfully'
    };
  } catch (err) {
    return {
      error: 'Error in updating the inventory'
    };
  }
};
