'use server';

import { db } from '@/src/server/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { auth } from '@/src/server/auth';
import { IMedia } from '@/src/_models/media.model';

export const createMedia = async (data: IMedia) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { fileKey, fileUrl, comments, mimeType } = data;

  const FormData = z.object({
    fileKey: z.string(),
    fileUrl: z.string().nullable(),
    mimeType: z.string().nullable(),
    comments: z.string().nullable()
  });

  const isValidData = FormData.parse({
    fileKey,
    fileUrl,
    comments,
    mimeType
  });

  if (!isValidData) {
    return {
      error: 'Invalid data'
    };
  }

  await db.media.create({
    data: {
      fileKey,
      fileUrl,
      comments,
      mimeType
    }
  });

  revalidatePath('/admin/media');
  return {
    message: 'Media created successfully'
  };
};

export const updateMedia = async (data: IMedia) => {
  const session = await auth();

  if (!session) {
    return {
      error: 'Unauthorized'
    };
  }

  const { id, fileKey, fileUrl, comments, mimeType } = data;

  const FormData = z.object({
    id: z.string(),
    fileKey: z.string(),
    fileUrl: z.string().nullable(),
    mimeType: z.string().nullable(),
    comments: z.string().nullable()
  });

  const isValidData = FormData.parse({
    id,
    fileKey,
    fileUrl,
    comments,
    mimeType
  });

  await db.media.update({
    where: {
      id: id as string
    },
    data: {
      fileKey,
      fileUrl,
      comments,
      mimeType
    }
  });

  revalidatePath('/admin/media');
  return {
    message: 'Media updated successfully'
  };
};

export const deleteMedia = async (mediaId: string) => {
  const media = await db.media.findUnique({
    where: {
      id: mediaId,
      deletedAt: null
    },
    include: {
      products: true,
      brands: true,
      categories: true,
      mediaOnVenues: true,
      homeBanners: true,
      blogs: true,
      testimonials: true,
      amenities: true
    }
  });

  if (
    (media?.amenities && media?.amenities.length > 0) ||
    (media?.blogs && media?.blogs.length > 0) ||
    (media?.brands && media?.brands.length > 0) ||
    (media?.categories && media?.categories.length > 0) ||
    (media?.homeBanners && media?.homeBanners.length > 0) ||
    (media?.mediaOnVenues && media?.mediaOnVenues.length > 0)
  ) {
    return {
      error: 'Cannot delete media as it is an associated record'
    };
  }
  try {
    await db.media.update({
      data: {
        deletedAt: new Date()
      },
      where: {
        id: mediaId
      }
    });
    revalidatePath('/manage-products/products');
    return {
      message: 'Media deleted successfully'
    };
  } catch (err) {
    return {
      error: 'Error in deleting media'
    };
  }
};
