import { Dialog } from 'primereact/dialog';
import Image from 'next/image';
import { IActiveLineItem, IOrderDetail } from './orders-table';
type IOrderDialog = {
  visible: boolean;
  currentOrderInfo: string[];
  currentLineItems: IActiveLineItem[];
  currentOrder: IOrderDetail | null;
  onHide: () => void;
};
const OrderDialog = ({ visible, currentOrderInfo, currentLineItems, currentOrder, onHide }: IOrderDialog) => {
  return (
    <>
      <Dialog header="Line Items under this order" visible={visible} style={{ width: '50vw' }} onHide={onHide}>
        {currentLineItems.map((item, index) => (
          <div className="mb-3" key={item.id}>
            <div className="font-semibold text-lg text-teal-600 mb-2">{`LineItem ${index + 1}`}</div>
            <div className="flex flex-row gap-4 align-items-start">
              <div className="relative w-9rem h-9rem">
                <Image src={item.productVariant.product.media[0]?.media.fileUrl || '/not-available.jpg'} alt="product-img" fill={true} />
              </div>
              <div className="flex flex-column gap-2">
                <div>{`Item name : ${item.productVariant.product.name}`}</div>
                <div>{`Actual item price : Rs. ${item.subTotalInCent / item.quantity / 100}`}</div>
                <div>{`Discount: Rs. ${item.discountInCent / 100}`}</div>
                <div>{`Final price of item : Rs. ${item.totalInCent / item.quantity / 100}`}</div>
                <div>{`Quantity : ${item.quantity}`}</div>
                <div className="font-semibold text-blue-700">
                  Total :<span> Rs. {item.totalInCent / 100}</span>
                </div>
              </div>
            </div>
          </div>
        ))}

        <div className="flex flex-column gap-1">
          <div>{currentOrderInfo[4]}</div>
          <div>{currentOrderInfo[0]}</div>
          <div>{currentOrderInfo[1]}</div>
          <div>{currentOrderInfo[2]}</div>
          <div className="font-semibold text-blue-700 text-lg">{currentOrderInfo[3]}</div>
        </div>

        {/* Razorpay Payment Information */}
        {currentOrder && (
          <div className="mt-4 p-3 border-1 border-300 border-round">
            <div className="font-semibold text-lg text-teal-600 mb-2">Payment Information</div>
            <div className="flex flex-column gap-2">
              <div>
                <span className="font-medium">Razorpay Order ID: </span>
                <span className="text-sm font-mono">{currentOrder.razorpay_order_id || 'Not available'}</span>
              </div>
              <div>
                <span className="font-medium">Razorpay Payment ID: </span>
                <span className="text-sm font-mono">{currentOrder.razorpay_payment_id || 'Not available'}</span>
              </div>
              <div>
                <span className="font-medium">Razorpay Status: </span>
                <span className="text-sm">{currentOrder.razorpay_status || 'Not available'}</span>
              </div>
              <div>
                <span className="font-medium">Order Status: </span>
                <span className={`text-sm font-medium ${
                  currentOrder.status === 'PAYMENT_SUCCESSFUL' ? 'text-green-600' :
                  currentOrder.status === 'PAYMENT_FAILED' ? 'text-red-600' :
                  currentOrder.status === 'PAYMENT_PENDING' ? 'text-orange-600' :
                  'text-gray-600'
                }`}>
                  {currentOrder.status}
                </span>
              </div>
            </div>
          </div>
        )}
      </Dialog>
    </>
  );
};

export default OrderDialog;
