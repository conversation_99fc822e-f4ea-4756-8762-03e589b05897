'use client';
import { IOrder } from '@/src/_models/order.model';
import { Demo } from '@/types';
import { OrderStatus } from '@repo/database';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Tag } from 'primereact/tag';
import { ChangeEvent, useRef, useState } from 'react';
import '../../../../../styles/global.css';
import { Dialog } from 'primereact/dialog';
import Image from 'next/image';
import OrderDialog from './order-dialog';
import ShipRocketDialog from './shipRocket-dialog';
import ShipRocketForm from './shipRocket-dialog';
import { api } from '@/src/trpc/react';
import { Toast } from 'primereact/toast';

export type IOrderDetail = {
  id: string;
  userId: string;
  status: string;
  subTotalInCent: number;
  taxesInCent: number;
  deliveryFeesInCent: number;
  totalInCent: number;
  couponId: null | string;
  addressId: string;
  lineItems: IActiveLineItem[];
  address: IActiveAddress;
  shiprocket_order_id: string | null;
  razorpay_order_id: string | null;
  razorpay_payment_id: string | null;
  razorpay_status: string | null;
};
export type IActiveLineItem = {
  id: string;
  orderId: string;
  productVariantId: string;
  perUnitPriceInCent: number;
  quantity: number;
  subTotalInCent: number;
  discountInCent: number;
  totalInCent: number;
  productVariant: {
    id: string;
    name: string;
    productId: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    product: {
      id: string;
      name: string;
      media: {
        mediaId: string;
        imageAltText: string | null;
        comment: string | null;
        media: {
          id: string;
          fileKey: string;
          fileUrl: string | null;
        };
      }[];
    };
  };
};
export type IActiveAddress = {
  id: string;
  name: string;
  mobile: string;
  houseNo: string;
  area: string;
  city: string;
  country: {
    id: string;
    name: string;
  };
  countryId: string;
  state: {
    id: string;
    name: string;
  };
  stateId: string;
  landmark: string;
  pincode: string;
  addressType: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  userId: string;
};
export type IShipRocket = {
  length: number;
  breadth: number;
  height: number;
  weight: number;
};
const OrdersTable = ({ orders }: { orders: IOrderDetail[] }) => {
  const [statuses] = useState([
    { label: 'Cancelled', value: 'CANCELLED' },
    { label: 'Delivered', value: 'DELIVERED' },
    { label: 'Out for Delivery', value: 'OUT_FOR_DELIVERY' },
    { label: 'Payment Failed', value: 'PAYMENT_FAILED' },
    { label: 'Payment Pending', value: 'PAYMENT_PENDING' },
    { label: 'Payment Successful', value: 'PAYMENT_SUCCESSFUL' },
    { label: 'Returned', value: 'RETURNED' }
  ]);
  const emptyForm: IShipRocket = {
    length: 0,
    breadth: 0,
    height: 0,
    weight: 0
  };
  const [selectedAdminUsers, setSelectedAdminUsers] = useState('');
  const [showShipRocketDialog, setShowShipRocketDialog] = useState<boolean>(false);
  const [visible, setVisible] = useState(false);
  const [shippingData, setShippingData] = useState<IShipRocket>(emptyForm);
  const [currentOrderInfo, setCurrentOrderInfo] = useState<string[]>([]);
  const [currentLineItems, setCurrentLineItems] = useState<IActiveLineItem[]>([]);
  const [orderId, setOrderId] = useState<string>('');
  const [currentOrder, setCurrentOrder] = useState<IOrderDetail | null>(null);
  const toast = useRef<Toast>(null);

  // Payment verification mutation
  const verifyPaymentMutation = api.orders.verifyPaymentStatus.useMutation({
    onSuccess: (data) => {
      toast.current?.show({
        severity: 'success',
        summary: 'Payment Verification',
        detail: data.message,
        life: 5000,
      });
      // Refresh the page to show updated status
      window.location.reload();
    },
    onError: (error) => {
      toast.current?.show({
        severity: 'error',
        summary: 'Payment Verification Failed',
        detail: error.message,
        life: 5000,
      });
    },
  });

  const handleVerifyPayment = (order: IOrderDetail) => {
    verifyPaymentMutation.mutate({ orderId: order.id });
  };

  const getSeverity = (status: string) => {
    switch (status) {
      case OrderStatus.CANCELLED:
        return 'danger';

      case OrderStatus.DELIVERED:
        return 'success';

      case OrderStatus.OUT_FOR_DELIVERY:
        return 'info';

      case OrderStatus.PAYMENT_FAILED:
        return 'warning';

      case OrderStatus.PAYMENT_PENDING:
        return 'warning';

      case OrderStatus.PAYMENT_SUCCESSFUL:
        return 'success';

      case OrderStatus.RETURNED:
        return 'danger';
    }
  };
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
  });

  const [globalFilter, setGlobalFilter] = useState('');
  const dt = useRef<DataTable<any>>(null);
  const onGlobalFilterChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let _filters: any = { ...filters };

    _filters.global.value = value;

    setFilters(_filters);
    setGlobalFilter(value);
  };
  const hideDialog = () => {
    setVisible(false);
    setShowShipRocketDialog(false);
  };
  const header = (
    <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
      <h5 className="m-0">Manage Orders</h5>
      <span className="block mt-2 md:mt-0 p-input-icon-left">
        <i className="pi pi-search" />
        <InputText type="search" value={globalFilter} onChange={onGlobalFilterChange} placeholder="Search..." />
      </span>
    </div>
  );

  const subTotalBodyTemplate = (rowData: IOrderDetail) => {
    return (
      <>
        <span className="p-column-title">SubTotalInCent</span>
        {rowData.subTotalInCent / 100}
      </>
    );
  };

  const totalInCentBodyTemplate = (rowData: IOrderDetail) => {
    return (
      <>
        <span className="p-column-title">TotalInCent</span>
        {rowData.totalInCent / 100}
      </>
    );
  };

  const userBodyTemplate = (rowData: IOrderDetail) => {
    return (
      <>
        <div>{rowData.address.name}</div>
        <div>{rowData.address.mobile}</div>
        <div>
          {rowData.address.houseNo}, {rowData.address.city}, {rowData.address.state.name}, {rowData.address.country.name}
        </div>
      </>
    );
  };

  const statusBodyTemplate = (rowData: IOrderDetail) => {
    return <Tag value={rowData.status} severity={getSeverity(rowData.status)} />;
  };
  const statusRowFilterTemplate = (options: any) => {
    return <MultiSelect value={options.value} options={statuses} onChange={(e) => options.filterApplyCallback(e.value, options.index)} optionLabel="label" placeholder="Select One" className="p-column-filter" showClear />;
  };
  const handleClickOnShipRocketBtn = (orderId: string) => {
    setShowShipRocketDialog(true);
    setShippingData({ ...emptyForm });
    setOrderId(orderId);
  };
  const showLineItemsDialog = (order: IOrderDetail) => {
    const orderInfo = [
      `SubTotal: Rs. ${order.subTotalInCent / 100}`,
      `Taxes: Rs. ${order.taxesInCent / 100}`,
      `Delivery Fees: Rs. ${order.deliveryFeesInCent / 100}`,
      `Total: Rs. ${order.totalInCent / 100}`,
      `Address: ${order.address.name}, ${order.address.houseNo}, ${order.address.area}, ${order.address.city}, ${order.address.pincode}`
    ];
    setCurrentOrderInfo(orderInfo);
    setCurrentLineItems(order.lineItems);
    setCurrentOrder(order);
    setVisible(true);
  };

  const actionBodyTemplate = (rowData: IOrderDetail) => {
    return (
      <>
        <div className="flex flex-row align-items-center gap-2">
          <Button className="p-2" severity="success" onClick={() => showLineItemsDialog(rowData)}>
            View Details
          </Button>

          {rowData.razorpay_order_id && (
            <Button
              className="p-2"
              severity="info"
              onClick={() => handleVerifyPayment(rowData)}
              loading={verifyPaymentMutation.isLoading}
              disabled={verifyPaymentMutation.isLoading}
            >
              Verify Payment
            </Button>
          )}

          {rowData.status === 'PAYMENT_SUCCESSFUL' && !rowData.shiprocket_order_id && (
            <Button onClick={() => handleClickOnShipRocketBtn(rowData.id)} className="p-2" severity="success">
              Move to ShipRocket
            </Button>
          )}
        </div>
      </>
    );
  };
  return (
    <>
      <Toast ref={toast} />
      <div className="grid crud-demo">
        <div className="col-12">
          <DataTable
            stripedRows
            ref={dt}
            value={orders}
            selection={selectedAdminUsers}
            onSelectionChange={(e) => setSelectedAdminUsers(e.value as any)}
            dataKey="id"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25]}
            className="datatable-responsive"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            // globalFilter={globalFilter}
            filters={filters}
            globalFilterFields={['id', 'status']}
            emptyMessage="No orders found."
            header={header}
            exportFilename="Orders"
          >
            <Column field="id" header="Id" sortable headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="address" filterField="address.name" filterMenuStyle={{ width: '14rem' }} header="User" sortable headerStyle={{ minWidth: '15rem' }} body={userBodyTemplate} filter filterElement={statusRowFilterTemplate}></Column>
            <Column field="status" filterMenuStyle={{ width: '14rem' }} header="status" sortable headerStyle={{ minWidth: '15rem' }} body={statusBodyTemplate} filter filterElement={statusRowFilterTemplate}></Column>
            <Column field="taxesInCent" header="taxesInCent" sortable headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="deliveryFeesInCent" header="deliveryFeesInCent" sortable headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="subTotalInCent" header="subTotalInCent" body={subTotalBodyTemplate} sortable headerStyle={{ minWidth: '15rem' }}></Column>
            <Column field="totalInCent" header="totalInCent" body={totalInCentBodyTemplate} sortable headerStyle={{ minWidth: '15rem' }}></Column>
            <Column body={actionBodyTemplate} headerStyle={{ minWidth: '20rem' }} frozen={true} />
          </DataTable>
        </div>
      </div>
      <OrderDialog currentLineItems={currentLineItems} currentOrderInfo={currentOrderInfo} currentOrder={currentOrder} visible={visible} onHide={hideDialog} />
      <ShipRocketForm orderId={orderId} shippingData={shippingData} showShipRocketDialog={showShipRocketDialog} onHide={hideDialog} />
    </>
  );
};
export default OrdersTable;
