// 'use server';

import { db } from '@/src/server/db';
import { auth } from '@/src/server/auth';
import { Skeleton } from 'primereact/skeleton';
import { Suspense } from 'react';
import OrdersTable from './orders-table';

export const dynamic = 'force-dynamic';

const OrdersPage = async () => {
  const session = await auth();
  const user = await db.user.findUnique({
    where: {
      email: session?.user?.email!,

      deletedAt: null
    }
  });
  const orders = await db.order.findMany({
    select: {
      id: true,
      userId: true,
      status: true,
      subTotalInCent: true,
      taxesInCent: true,
      deliveryFeesInCent: true,
      totalInCent: true,
      couponId: true,
      addressId: true,
      shiprocket_order_id: true,
      razorpay_order_id: true,
      razorpay_payment_id: true,
      razorpay_status: true,
      createdAt: true,
      updatedAt: true,
      length: true,
      breadth: true,
      height: true,
      weight: true,
      lineItems: {
        include: {
          productVariant: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  media: {
                    select: {
                      mediaId: true,
                      imageAltText: true,
                      comment: true,
                      media: {
                        select: {
                          id: true,
                          fileKey: true,
                          fileUrl: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      address: {
        include: {
          state: {
            select: {
              id: true,
              name: true
            }
          },
          country: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }
    },
    where: {
      userId: user?.id
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  console.log('orders are', orders);

  const formattedOrders = orders.map((o) => ({
    ...o,
    lineItems: o.lineItems.map((l) => ({
      ...l,
      productVariant: {
        ...l.productVariant,
        weightInKgForShipping: Number(l.productVariant.weightInKgForShipping)
      }
    })),
    length: Number(o.length),
    breadth: Number(o.breadth),
    height: Number(o.height),
    weight: Number(o.weight)
  }));
  console.log('formatted orders are', formattedOrders);
  return (
    <>
      <Suspense fallback={<Skeleton width="100%" height="100px" />}>
        <OrdersTable orders={formattedOrders} />
      </Suspense>
    </>
  );
};

export default OrdersPage;
