'use client';

import React, { useState } from 'react';
import { Skeleton } from 'primereact/skeleton';
import TransactionsTable from './transactions-table';
import { startOfMonth, endOfMonth } from 'date-fns';
import { api } from '@/src/trpc/react';

const TransactionsClient = () => {
  // Set default date range to current month
  const [fromDate, setFromDate] = useState<Date>(startOfMonth(new Date()));
  const [toDate, setToDate] = useState<Date>(endOfMonth(new Date()));

  // Fetch transactions data using tRPC
  const { data, isLoading, error } = api.transactions.getTransactions.useQuery(
    {
      fromDate,
      toDate
    },
    {
      refetchOnWindowFocus: false
    }
  );

  // Handle date range changes from the table component
  const handleDateRangeChange = (newFromDate: Date, newToDate: Date) => {
    setFromDate(newFromDate);
    setToDate(newToDate);
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="border-round border-1 surface-border p-4">
          <div className="flex mb-3">
            <Skeleton width="100%" height="50px" />
          </div>
          <Skeleton width="100%" height="400px" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="border-round border-1 surface-border p-4">
          <div className="flex mb-3 justify-content-center">
            <div className="text-red-500">Error loading transactions: {error.message}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <TransactionsTable
      orders={data?.orders || []}
      gymMemberships={data?.gymMemberships || []}
      venueMemberships={data?.venueMemberships || []}
      venueBookings={data?.venueBookings || []}
      initialFromDate={fromDate}
      initialToDate={toDate}
      onDateRangeChange={handleDateRangeChange}
    />
  );
};

export default TransactionsClient;
