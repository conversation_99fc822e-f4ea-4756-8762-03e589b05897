'use client';

import React, { useRef, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { FilterMatchMode } from 'primereact/api';
import { format } from 'date-fns';
import { Calendar } from 'primereact/calendar';


type TransactionType = 'All' | 'Orders' | 'Gym Memberships' | 'Venue Memberships' | 'Venue Bookings';

interface TransactionsTableProps {
  orders: any[];
  gymMemberships: any[];
  venueMemberships: any[];
  venueBookings: any[];
  initialFromDate: Date;
  initialToDate: Date;
  onDateRangeChange?: (fromDate: Date, toDate: Date) => void;
}

const TransactionsTable = ({
  orders,
  gymMemberships,
  venueMemberships,
  venueBookings,
  initialFromDate,
  initialToDate,
  onDateRangeChange
}: TransactionsTableProps) => {
  const [selectedType, setSelectedType] = useState<TransactionType>('All');
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS }
  });
  const [dates, setDates] = useState<Date[]>([initialFromDate, initialToDate]);
  const dt = useRef<DataTable<any>>(null);

  const updateDateRange = (newDates: Date[]) => {
    console.log('dates', newDates);

    setDates(newDates);

    if (!newDates || newDates.length !== 2 || !newDates[0] || !newDates[1]) return;

    // Call the parent component's date range change handler if provided
    if (onDateRangeChange) {
      onDateRangeChange(newDates[0], newDates[1]);
    }
  };

  // Combine and format all transactions
  const formatTransactions = () => {
    const formattedOrders = orders.map(order => {
      // Format line items for display
      const lineItemsText = order.lineItems?.map((item: any) =>
        `${item.productVariant.product.name} - ${item.productVariant.name} (Qty: ${item.quantity})`
      ).join(', ') || 'No items';

      return {
        ...order,
        type: 'Order',
        amount: order.totalInCent / 100,
        userName: `${order.user.firstName} ${order.user.lastName}`,
        details: `Products: ${lineItemsText}`
      };
    });

    const formattedGymMemberships = gymMemberships.map(membership => {
      const venueName = membership.gymMembership.venue?.name || 'N/A';
      const duration = membership.gymMembership.monthsInPlan || 0;
      const startDate = membership.startDate ? format(new Date(membership.startDate), 'dd/MM/yyyy') : 'N/A';
      const endDate = membership.endDate ? format(new Date(membership.endDate), 'dd/MM/yyyy') : 'N/A';

      return {
        ...membership,
        type: 'Gym Membership',
        amount: membership.amountInCents / 100,
        userName: `${membership.user.firstName} ${membership.user.lastName}`,
        details: `${membership.gymMembership.name} | Duration: ${duration} months | Venue: ${venueName} | Period: ${startDate} to ${endDate}`
      };
    });

    const formattedVenueMemberships = venueMemberships.map(membership => {
      const venueName = membership.venueGameMemberShip.venue?.name || 'N/A';
      const gameName = membership.venueGameMemberShip.game?.name || 'N/A';
      const startDate = membership.startDate ? format(new Date(membership.startDate), 'dd/MM/yyyy') : 'N/A';
      const endDate = membership.endDate ? format(new Date(membership.endDate), 'dd/MM/yyyy') : 'N/A';

      return {
        ...membership,
        type: 'Venue Membership',
        amount: membership.amountInCents / 100,
        userName: `${membership.user.firstName} ${membership.user.lastName}`,
        details: `${membership.venueGameMemberShip.name} | Game: ${gameName} | Venue: ${venueName} | Period: ${startDate} to ${endDate}`
      };
    });

    const formattedVenueBookings = venueBookings.map(booking => {
      const venueName = booking.venue?.name || 'N/A';
      const gameSlots = booking.gameSlots || [];

      // Format game slots information
      const slotsInfo = gameSlots.map((slot: any) => {
        const gameName = slot.game?.name || 'Unknown';
        const startTime = slot.startTime ? format(new Date(slot.startTime), 'dd/MM/yyyy HH:mm') : 'N/A';
        const endTime = slot.endTime ? format(new Date(slot.endTime), 'HH:mm') : 'N/A';
        return `${gameName}: ${startTime} - ${endTime}`;
      }).join(', ') || 'No slots';

      return {
        ...booking,
        type: 'Venue Booking',
        amount: booking.totalAmount / 100,
        userName: `${booking.user.firstName} ${booking.user.lastName}`,
        details: `Venue: ${venueName} | Slots: ${slotsInfo}`
      };
    });

    const allTransactions = [
      ...formattedOrders,
      ...formattedGymMemberships,
      ...formattedVenueMemberships,
      ...formattedVenueBookings
    ];

    if (selectedType === 'All') return allTransactions;
    return allTransactions.filter(t => t.type === selectedType);
  };

  const header = (
    <div className="flex flex-wrap gap-2 align-items-center justify-content-between">
      <h4 className="m-0">Transactions</h4>
      <div className="flex flex-wrap gap-2 align-items-center">
        <div className="flex flex-column gap-2">
          {/* <label>Date Range</label> */}
          <Calendar
            value={dates}
            onChange={(e) => {
              console.log('e.value', e.value);
              updateDateRange(e.value as Date[])
            }}
            selectionMode="range"
            readOnlyInput
            dateFormat="dd/MM/yy"
            placeholder="Select Date Range"
            style={{ width: '320px' }}
          />
        </div>
        <div className="flex gap-2 align-items-end">
          {/* <Dropdown
            value={selectedType}
            onChange={(e) => setSelectedType(e.value)}
            options={['All', 'Orders', 'Gym Memberships', 'Venue Memberships', 'Venue Bookings']}
            placeholder="Select Type"
          /> */}
          <Button
            type="button"
            icon="pi pi-file-excel"
            severity="success"
            onClick={() => {
              if (dt.current) {
                // const fromDateStr = format(dates[0], 'yyyy-MM-dd');
                // const toDateStr = format(dates[1], 'yyyy-MM-dd');
                dt.current.exportCSV();
                  // {
                  // fileName: `transactions_${fromDateStr}_to_${toDateStr}.csv`
                // });
              }
            }}
            tooltip="Export to CSV"
            tooltipOptions={{ position: 'top' }}
          />
        </div>
      </div>
    </div>
  );

  const dateBodyTemplate = (rowData: any) => {
    return format(new Date(rowData.createdAt), 'dd/MM/yyyy hh:mm a');
  };

  const amountBodyTemplate = (rowData: any) => {
    return `₹${rowData.amount}`;
  };

  return (
    <div className="grid crud-demo">
      <div className="col-12">
        <DataTable
          ref={dt}
          value={formatTransactions()}
          dataKey="id"
          paginator
          rows={10}
          rowsPerPageOptions={[5, 10, 25]}
          className="datatable-responsive"
          paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
          currentPageReportTemplate="Showing {first} to {last} of {totalRecords} transactions"
          filters={filters}
          filterDisplay="menu"
          globalFilterFields={['userName', 'user.email', 'user.mobileNumber', 'type', 'details']}
          emptyMessage="No transactions found."
          header={header}
          stripedRows
        >
          <Column field="type" header="Type" sortable />
          <Column field="details" header="Details" sortable style={{ width: '40%', whiteSpace: 'normal', wordBreak: 'break-word' }} />
          <Column field="userName" header="User Name" sortable />
          <Column field="user.email" header="Email" sortable />
          <Column field="user.mobileNumber" header="Phone" sortable />
          <Column field="amount" header="Amount" body={amountBodyTemplate} sortable />
          <Column field="createdAt" header="Date" body={dateBodyTemplate} sortable />
        </DataTable>
      </div>
    </div>
  );
};

export default TransactionsTable;