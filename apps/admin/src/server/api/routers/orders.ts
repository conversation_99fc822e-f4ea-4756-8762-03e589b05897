import { createTRPCRouter, protectedProcedure } from '@/src/server/api/trpc';
import { z } from 'zod';
import { OrderStatus } from '@repo/database';
import { db } from '@/src/server/db';
import Razorpay from 'razorpay';
import { TRPCError } from '@trpc/server';
import { env } from '../../../../env';

export const ordersRouter = createTRPCRouter({
  verifyPaymentStatus: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Find the order with Razorpay details
        const order = await db.order.findFirst({
          where: {
            id: input.orderId,
          },
          select: {
            id: true,
            razorpay_order_id: true,
            razorpay_payment_id: true,
            razorpay_status: true,
            status: true,
            totalInCent: true,
          },
        });

        if (!order) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Order not found',
          });
        }

        if (!order.razorpay_order_id) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'No Razorpay order ID found for this order',
          });
        }

        // Initialize Razorpay instance
        const razorpayInstance = new Razorpay({
          key_id: env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,
          key_secret: env.RAZORPAY_KEY_SECRET!,
        });

        // Fetch order details from Razorpay
        const razorpayOrder = await razorpayInstance.orders.fetch(order.razorpay_order_id);

        // Fetch payment details if payment ID exists
        let razorpayPayment = null;
        if (order.razorpay_payment_id) {
          try {
            razorpayPayment = await razorpayInstance.payments.fetch(order.razorpay_payment_id);
          } catch (error) {
            console.error('Error fetching payment details:', error);
          }
        }

        // Determine the actual payment status
        let actualStatus: OrderStatus = OrderStatus.PAYMENT_PENDING;
        let paymentVerified = false;

        if (razorpayOrder.status === 'paid' && razorpayOrder.amount_paid > 0) {
          actualStatus = OrderStatus.PAYMENT_SUCCESSFUL;
          paymentVerified = true;
        } else if (razorpayOrder.status === 'attempted') {
          actualStatus = OrderStatus.PAYMENT_FAILED;
        }

        // Update order status if it differs from current status
        let statusUpdated = false;
        if (order.status !== actualStatus) {
          await db.order.update({
            where: { id: order.id },
            data: {
              status: actualStatus,
              razorpay_status: razorpayOrder.status,
            },
          });
          statusUpdated = true;
        }

        return {
          success: true,
          paymentVerified,
          statusUpdated,
          razorpayOrder: {
            id: razorpayOrder.id,
            status: razorpayOrder.status,
            amount: razorpayOrder.amount,
            amount_paid: razorpayOrder.amount_paid,
            currency: razorpayOrder.currency,
            created_at: razorpayOrder.created_at,
          },
          razorpayPayment: razorpayPayment ? {
            id: razorpayPayment.id,
            status: razorpayPayment.status,
            amount: razorpayPayment.amount,
            currency: razorpayPayment.currency,
            method: razorpayPayment.method,
            captured: razorpayPayment.captured,
            created_at: razorpayPayment.created_at,
          } : null,
          currentOrderStatus: actualStatus,
          message: statusUpdated
            ? `Payment status updated to ${actualStatus}`
            : `Payment status confirmed as ${actualStatus}`,
        };
      } catch (error) {
        console.error('Error verifying payment status:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to verify payment status from Razorpay',
        });
      }
    }),
});
