import { createTRPCRouter, protectedProcedure } from '@/src/server/api/trpc';
import { z } from 'zod';
import { OrderStatus, GymMembershipBookingStatus, VenueBookingStatus, VenueMembershipBookingStatus } from '@repo/database';
import { db } from '@/src/server/db';

export const transactionsRouter = createTRPCRouter({
  getTransactions: protectedProcedure
    .input(
      z.object({
        fromDate: z.date(),
        toDate: z.date()
      })
    )
    .query(async ({ ctx, input }) => {
      // Add one day to toDate to include the end date in the range (as the database query uses < not <=)
      const toDatePlusOne = new Date(input.toDate);
      toDatePlusOne.setDate(toDatePlusOne.getDate() + 1);

      // Fetch orders
      const orders = await db.order.findMany({
        where: {
          status: OrderStatus.PAYMENT_SUCCESSFUL,
          createdAt: {
            gte: input.fromDate,
            lt: toDatePlusOne
          }
        },
        select: {
          id: true,
          totalInCent: true,
          createdAt: true,
          lineItems: {
            select: {
              quantity: true,
              productVariant: {
                select: {
                  name: true,
                  product: {
                    select: {
                      name: true
                    }
                  }
                }
              }
            }
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              mobileNumber: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Fetch gym memberships
      const gymMemberships = await db.gymMembershipBooking.findMany({
        where: {
          status: GymMembershipBookingStatus.PAYMENT_COMPLETED,
          createdAt: {
            gte: input.fromDate,
            lt: toDatePlusOne
          }
        },
        select: {
          id: true,
          amountInCents: true,
          createdAt: true,
          startDate: true,
          endDate: true,
          gymMembership: {
            select: {
              name: true,
              monthsInPlan: true,
              venue: {
                select: {
                  name: true
                }
              }
            }
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              mobileNumber: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Fetch venue memberships
      const venueMemberships = await db.venueMembershipBooking.findMany({
        where: {
          status: VenueMembershipBookingStatus.PAYMENT_COMPLETED,
          createdAt: {
            gte: input.fromDate,
            lt: toDatePlusOne
          }
        },
        select: {
          id: true,
          amountInCents: true,
          createdAt: true,
          startDate: true,
          endDate: true,
          venueGameMemberShip: {
            select: {
              name: true,
              venue: {
                select: {
                  name: true
                }
              },
              game: {
                select: {
                  name: true
                }
              }
            }
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              mobileNumber: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Fetch venue bookings
      const venueBookings = await db.venueBooking.findMany({
        where: {
          status: VenueBookingStatus.PAYMENT_COMPLETED,
          createdAt: {
            gte: input.fromDate,
            lt: toDatePlusOne
          }
        },
        select: {
          id: true,
          totalAmount: true,
          createdAt: true,
          gameSlots: {
            select: {
              startTime: true,
              endTime: true,
              game: {
                select: {
                  name: true
                }
              }
            }
          },
          venue: {
            select: {
              name: true
            }
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              mobileNumber: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return {
        orders,
        gymMemberships,
        venueMemberships,
        venueBookings
      };
    })
});
