/* eslint-disable max-lines-per-function */
import type { ConfigContext, ExpoConfig } from '@expo/config';
import type { AppIconBadgeConfig } from 'app-icon-badge/types';

import { ClientEnv, Env } from './env';

const appIconBadgeConfig: AppIconBadgeConfig = {
  enabled: Env.APP_ENV !== 'production',
  badges: [
    {
      text: Env.APP_ENV,
      type: 'banner',
      color: 'white',
    },
    {
      text: Env.VERSION.toString(),
      type: 'ribbon',
      color: 'white',
    },
  ],
};

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'Flexit Fitness',
  description: 'Flexit Fitness',
  owner: 'nextfly-icloud',
  scheme: 'com.nextflytech.flexitfitness',
  slug: 'flexit-fitness-shop',
  version: Env.VERSION.toString(),
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'light',
  newArchEnabled: true,
  updates: {
    url: 'https://u.expo.dev/57d3f13c-1f9f-426e-952c-7b5ecb34280e',
  },
  runtimeVersion: {
    policy: 'appVersion',
  },
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.nextflytech.flexitfitness',
    usesAppleSignIn: true,
    infoPlist: {
      NSCameraUsageDescription: 'Allow $(PRODUCT_NAME) to access your Camera',
      NSPhotoLibraryUsageDescription:
        'Allow $(PRODUCT_NAME) to access your Photos',
      NSPhotoLibraryAddUsageDescription:
        'Allow $(PRODUCT_NAME) to save photos to your photo library',
      NSMicrophoneUsageDescription:
        'Allow $(PRODUCT_NAME) to access your Microphone',
      GIDClientID:
        '1044169187752-6rksfh2ttujmuo9abt4ln74klmgd0cun.apps.googleusercontent.com',
      CFBundleURLTypes: [
        {
          CFBundleURLSchemes: [
            'com.googleusercontent.apps.1044169187752-6rksfh2ttujmuo9abt4ln74klmgd0cun',
          ],
        },
      ],
      ITSAppUsesNonExemptEncryption: false,
    },
  },
  experiments: {
    typedRoutes: true,
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#2E3C4B',
    },
    package: 'com.nextflytech.flexitfitness',
    versionCode: 9,
  },
  web: {
    favicon: './assets/favicon.png',
    bundler: 'metro',
  },

  plugins: [
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
        locationAlwaysPermission: 'Allow $(PRODUCT_NAME) to use your location.',
        locationWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
      },
    ],
    [
      'expo-calendar',
      {
        calendarPermission: 'The app needs to access your calendar.',
      },
    ],
    [
      'expo-splash-screen',
      {
        backgroundColor: '#FFFFFF',
        image: './assets/splash-icon.png',
        dark: {
          image: './assets/splash-icon.png',
          backgroundColor: '#000000',
        },

        imageWidth: 150,
      },
    ],
    [
      'expo-font',
      {
        fonts: [
          './assets/fonts/Inter.ttf',
          './assets/fonts/Archivo-VariableFont.ttf',
          './assets/fonts/NotoSans-VariableFont.ttf',
        ],
      },
    ],
    'expo-secure-store',
    'expo-localization',
    'expo-router',
    ['app-icon-badge', appIconBadgeConfig],
    ['react-native-edge-to-edge'],
    [
      'expo-image-picker',
      {
        photosPermission: 'Allow $(PRODUCT_NAME) to access your Photos',
        cameraPermission: 'Allow $(PRODUCT_NAME) to access your Camera',
        microphonePermission: true,
      },
    ],
    'expo-apple-authentication',
    [
      '@react-native-google-signin/google-signin',
      {
        iosUrlScheme:
          'com.googleusercontent.apps.1044169187752-6rksfh2ttujmuo9abt4ln74klmgd0cun',
      },
    ],
    [
      '@sentry/react-native/expo',
      {
        url: 'https://sentry.io/',
        project: 'flexit-mobile',
        organization: 'flexit-fitness',
      },
    ],
  ],
  extra: {
    ...ClientEnv,
    eas: {
      projectId: '57d3f13c-1f9f-426e-952c-7b5ecb34280e',
    },
  },
});
