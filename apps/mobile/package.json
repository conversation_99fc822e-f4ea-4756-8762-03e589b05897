{"name": "flexit", "version": "0.0.9", "private": true, "main": "./entry.js", "scripts": {"start": "cross-env EXPO_NO_DOTENV=1 expo start", "start:clean": "cross-env EXPO_NO_DOTENV=1 expo start -c", "prebuild": "cross-env EXPO_NO_DOTENV=1 pnpm expo prebuild", "android": "cross-env EXPO_NO_DOTENV=1 expo run:android", "ios": "cross-env EXPO_NO_DOTENV=1 expo run:ios", "web": "cross-env EXPO_NO_DOTENV=1 expo start --web", "xcode": "xed -b ios", "doctor": "npx expo-doctor@latest", "preinstall": "npx only-allow pnpm", "start:preview": "cross-env APP_ENV=preview pnpm run start", "prebuild:preview": "cross-env APP_ENV=preview pnpm run prebuild", "prebuild:development": "cross-env APP_ENV=development pnpm run prebuild", "android:preview": "cross-env APP_ENV=preview pnpm run android", "ios:preview": "cross-env APP_ENV=preview pnpm run ios", "start:production": "cross-env APP_ENV=production pnpm run start", "prebuild:production": "cross-env APP_ENV=production pnpm run prebuild", "android:production": "cross-env APP_ENV=production pnpm run android", "ios:production": "cross-env APP_ENV=production pnpm run ios", "build:development:ios": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform ios", "build:development:android": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform android ", "build:preview:ios": "cross-env APP_ENV=preview EXPO_NO_DOTENV=1 eas build --profile preview --platform ios", "build:preview:android": "cross-env APP_ENV=preview EXPO_NO_DOTENV=1 eas build --profile preview --platform android ", "build:production:ios": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform ios", "build:production:android": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform android ", "app-release": "cross-env SKIP_BRANCH_PROTECTION=true np --no-publish --no-cleanup --no-release-draft", "version": "pnpm run prebuild && git add .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc  --noemit", "lint:translations": "eslint ./src/translations/ --fix --ext .json  ", "test": "jest", "check-all": "pnpm run lint && pnpm run type-check && pnpm run lint:translations && pnpm run test", "test:ci": "pnpm run test --coverage", "test:watch": "pnpm run test --watch", "install-maestro": "curl -Ls 'https://get.maestro.mobile.dev' | bash", "e2e-test": "maestro test .maestro/ -e APP_ID=com.obytes.development"}, "dependencies": {"@azure/core-asynciterator-polyfill": "^1.0.2", "@expo/metro-runtime": "^4.0.1", "@gorhom/bottom-sheet": "^5.0.5", "@hookform/resolvers": "^3.9.0", "@react-native-community/datetimepicker": "8.2.0", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@repo/customer-api": "workspace:*", "@sentry/react-native": "^6.10.0", "@shopify/flash-list": "1.7.3", "@tanstack/react-query": "^5.62.0", "@trpc/client": "11.0.0-rc.477", "@trpc/react-query": "11.0.0-rc.477", "@trpc/server": "11.0.0-rc.477", "@types/react-navigation": "^3.4.0", "app-icon-badge": "^0.1.2", "axios": "^1.7.5", "expo": "~52.0.46", "expo-apple-authentication": "~7.1.3", "expo-auth-session": "^6.0.3", "expo-blur": "~14.0.3", "expo-calendar": "~14.0.5", "expo-constants": "~17.0.8", "expo-crypto": "^14.0.2", "expo-dev-client": "~5.0.20", "expo-font": "~13.0.4", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.5", "expo-localization": "~16.0.0", "expo-location": "^18.0.10", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "expo-web-browser": "^14.0.2", "i18next": "^23.14.0", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "moti": "^0.29.0", "nativewind": "^4.1.21", "react": "18.3.1", "react-content-loader": "^7.0.2", "react-dom": "18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.1", "react-native": "0.76.9", "react-native-edge-to-edge": "^1.1.2", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.20.2", "react-native-haptic-feedback": "^2.3.3", "react-native-keyboard-controller": "^1.16.8", "react-native-mmkv": "~3.1.0", "react-native-otp-entry": "^1.8.0", "react-native-pager-view": "6.5.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.16.7", "react-native-render-html": "^6.3.4", "react-native-restart": "0.0.27", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "~15.8.0", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.13", "react-query-kit": "^3.3.0", "rn-eventsource-reborn": "^1.0.5", "tailwind-variants": "^0.2.1", "web-streams-polyfill": "^4.1.0", "zod": "3.22.4", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.26.0", "@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@dev-plugins/react-query": "^0.2.0", "@expo/config": "~10.0.3", "@react-native-community/cli": "^15.1.3", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react-native": "^12.7.2", "@types/i18n-js": "^3.8.9", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.15", "@types/lodash.memoize": "^4.1.9", "@types/react": "~18.3.12", "@types/react-native-razorpay": "^2.2.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-expo": "^7.1.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-i18n-json": "^4.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-compiler": "19.0.0-beta-a7bf2bd-20241110", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.15.2", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-unicorn": "^46.0.1", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^9.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "~52.0.5", "jest-junit": "^16.0.0", "lint-staged": "^15.2.9", "np": "^10.0.7", "prettier": "^3.3.3", "tailwindcss": "3.4.4", "ts-jest": "^29.1.2", "typescript": "^5.3.3"}, "repository": {"type": "git", "url": "git+https://github.com/user/repo-name.git"}, "packageManager": "pnpm@9.12.3", "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-restart"]}}, "install": {"exclude": ["eslint-config-expo"]}}, "osMetadata": {"initVersion": "7.0.2"}}