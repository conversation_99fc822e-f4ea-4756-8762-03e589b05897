import React from 'react';
import AntDesign from '@expo/vector-icons/AntDesign';
import {
  <PERSON><PERSON>,
  EmptyList,
  FocusAwareStatusBar,
  Text,
  View,
} from '@/components/ui';
import { Image, ImageBackground } from 'expo-image';
import {
  Dimensions,
  Pressable,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import CustomImage from '@/components/ui/image';
import ProductCardItem from '@/components/homepage/product-card-item';
import { ButtonOptimised } from '@/components/ui/button-optimised';
import { router } from 'expo-router';
import { api } from '@/utils/api';
import LoadingPage from '@/components/loading-page';
import * as Browser from 'expo-web-browser';
import PagerView from 'react-native-pager-view';
import { useAuth } from '@/lib/auth';
import Ionicons from '@expo/vector-icons/Ionicons';
import { format } from 'date-fns';
import { GameMembershipCard } from '@/components/homepage/game-membership';
import { SlotsTimelineCard } from '@/components/slot-booking-components';
import { MembershipStatusCard } from '@/components/membership-components';

const AuthenticatedHome = () => {
  const { data, isPending, isError } = api.home.protectedHomePage.useQuery();
  console.log('data activeGymMemberships', data?.activeGymMemberships);
  return (
    <View>
      {data?.activeGymMemberships && data?.activeGymMemberships.length > 0 && (
        <View className="my-2">
          <View className="flex-row items-center justify-between px-5">
            <Text className="text-xl font-archivo">Gym Membership</Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="flex gap-5"
          >
            {data?.activeGymMemberships.map((item) => (
              <View
                key={item.id}
                className="ml-5"
                style={{ width: Dimensions.get('screen').width * 0.9 }}
              >
                <MembershipStatusCard item={item} />
              </View>
            ))}
          </ScrollView>
        </View>
      )}
      {data?.activeSlotSubscriptions &&
        data?.activeSlotSubscriptions.length > 0 && (
          <View className="my-2">
            <View className="flex-row items-center justify-between px-5">
              <Text className="text-xl font-archivo">Game Subscription</Text>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="flex gap-5"
            >
              {data?.activeSlotSubscriptions.map((subscription) => (
                <View
                  key={subscription.id}
                  className="ml-5"
                  style={{ width: Dimensions.get('screen').width * 0.9 }}
                >
                  <GameMembershipCard subscription={subscription} />
                </View>
              ))}
            </ScrollView>
          </View>
        )}
      {data?.upcomingSlotBookings && data?.upcomingSlotBookings.length > 0 && (
        <View className="my-2">
          <View className="flex-row items-center justify-between px-5">
            <Text className="text-xl font-archivo">Book Slots</Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="flex gap-5"
          >
            {data?.upcomingSlotBookings.map((item) => (
              <View
                key={item.id}
                className="ml-5"
                style={{ width: Dimensions.get('screen').width * 0.9 }}
              >
                <SlotsTimelineCard booking={item} />
              </View>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

export default function Home() {
  const authStatus = useAuth.use.status();
  const { data, isPending, isError } = api.home.getHomeData.useQuery();

  const goToStore = () => {
    console.log('go to store');
    router.push('/store');
  };

  if (isPending) {
    return <LoadingPage />;
  }

  const handleBannerPress = (banner: {
    id: string;
    media: {
      fileUrl: string | null;
    } | null;
    url: string | null;
  }) => {
    console.log('banner', banner);

    if (!banner.url) {
      return;
    }
    console.log('banner.url', banner.url);
    console.log(
      'banner.url gym-membership',
      banner.url.indexOf('/gym-membership'),
    );
    console.log('banner.url bookslot', banner.url.indexOf('/bookslot'));

    if (banner.url.indexOf('/gym-membership') !== -1) {
      router.push('/membership');
      return;
    }

    if (banner.url.indexOf('/bookslot') !== -1) {
      router.push('/calendar');
      return;
    }

    Browser.openBrowserAsync(banner.url);
  };

  console.log(
    'data',
    Dimensions.get('window').width - 32,
    ((Dimensions.get('window').width - 32) / 35) * 9,
  );

  if (isError) {
    return <View className='flex-1 justify-center items-center'>
      <Text className="text-red-500 text-lg font-semibold">
        An error occurred. Please try again later.
      </Text>
    </View>;
  }

  return (
    <ScrollView className="">
      <PagerView
        className="flex-1 aspect-[80/33] w-full"
        style={{ width: Dimensions.get('window').width, aspectRatio: 80 / 33 }}
        initialPage={0}
      >
        {data?.homeBanners.filter((banner) => !!banner?.media).map((banner) => (
          <Pressable key={banner.id} onPress={() => handleBannerPress(banner)}>
            <Image
              source={{ uri: banner?.media?.fileUrl }}
              className="w-full aspect-auto min-h-40"
              contentFit="cover"
              style={{
                width: Dimensions.get('window').width,
                aspectRatio: 80 / 33,
              }}
            />
          </Pressable>
        ))}
      </PagerView>
      {authStatus === 'signIn' && <AuthenticatedHome />}
      <View className="flex-row items-center justify-between px-5">
        <Text className="text-xl font-archivo">Best Sellers</Text>
        <ButtonOptimised
          label="See All"
          variant="link"
          className="font-normal"
          onPress={goToStore}
        >
          <Text className="text-sm font-normal">See All</Text>
          <AntDesign name="right" size={20} color="#2AA952" />
        </ButtonOptimised>
      </View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {data?.bestsellerProducts.map((product) => (
          <ProductCardItem key={product.id} product={product} />
        ))}
      </ScrollView>
      <View className="my-8 mx-5">
        <Pressable onPress={() => router.push('/calendar')}>
          <Image
            source={require('../../../assets/images/book-slot.png')}
            style={{
              aspectRatio: 390 / 206,
              width: '100%',
            }}
            contentFit="contain"
          />
        </Pressable>
      </View>
      <View className="my-2 mx-5">
        <Pressable
          onPress={() =>
            Browser.openBrowserAsync('https://flexitshop.in/contact-us')
          }
        >
          <Image
            source={require('../../../assets/images/contact-us.png')}
            style={{
              aspectRatio: 39 / 13,
              width: '100%',
            }}
            contentFit="contain"
          />
        </Pressable>
      </View>
    </ScrollView>
  );
}
