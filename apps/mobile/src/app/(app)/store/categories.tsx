import {
  ActivityIndicator,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, { useState } from 'react';
import { ScreensHeader } from '@/components/screens-header';
import CustomImage from '@/components/ui/image';
import { DrawerContentComponentProps } from '@react-navigation/drawer';
import Entypo from '@expo/vector-icons/Entypo';
import { api } from '@/utils/api';
import { router } from 'expo-router';
import { capitalizeWords } from '@/utils/text-formatting';

interface CategoryListProps {
  id: string;
  title: string;
  iconSource?: string;
  itemsList: {
    id: string;
    name: string;
    slug: string;
  }[];
  onPress?: () => void;
}

const CategoryList = ({ item }: { item: CategoryListProps }) => {
  const [expand, setExpand] = useState(true);
  const toggleExpand = () => {
    setExpand((prev) => !prev);
  };

  // Navigate to category products page
  const navigateToCategory = () => {
    router.push(`/store/category/${item.id}?type=parent`);
  };

  // Navigate to child category products page
  const navigateToChildCategory = (childCategoryId: string) => {
    router.push(`/store/category/${childCategoryId}?type=child`);
  };

  return (
    <View key={item.id}>
      <Pressable
        key={`${item.id}`}
        className="py-2 flex-row items-center justify-between border-b border-text-50"
        onPress={toggleExpand}
      >
        <View className="flex-row items-center gap-3">
          <Text
            className={`text-base font-medium font-noto ${expand ? 'text-primary-500' : 'text-text-700'}`}
          >
            {capitalizeWords(item.title)}
          </Text>
        </View>

        <View className="flex-row items-center">
          {/* <Pressable
            className="mr-4 px-3 py-1 bg-primary-500 rounded-md"
            onPress={navigateToCategory}
          >
            <Text className="text-xs font-medium text-white">View All</Text>
          </Pressable> */}

          <Entypo
            name={expand === true ? 'chevron-up' : 'chevron-down'}
            size={24}
            color="#2AA952"
          />
        </View>
      </Pressable>
      {/* list of options */}
      {expand &&
        item.itemsList.map((childCategory) => (
          <Pressable
            className="mb-2 py-2 pl-4 flex-row justify-between items-center"
            key={childCategory.id}
            onPress={() => navigateToChildCategory(childCategory.id)}
          >
            <Text className="text-base font-normal font-noto text-black/70">
              {capitalizeWords(childCategory.name)}
            </Text>
            <Entypo name="chevron-right" size={18} color="#2AA952" />
          </Pressable>
        ))}
    </View>
  );
};

const Categories = (_props: DrawerContentComponentProps) => {
  const [categorySelected, setCategorySelected] = useState<string>();

  // Fetch categories from API
  const {
    data: categoriesData,
    isLoading,
    isError,
  } = api.parentCategory.getParentCategoriesWithChildren.useQuery();

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ScreensHeader Heading1="Categories" />
        <ActivityIndicator size="large" color="#2AA952" />
      </View>
    );
  }

  if (isError || !categoriesData || categoriesData.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        <ScreensHeader Heading1="Categories" />
        <Text>No categories available</Text>
      </View>
    );
  }

  // Transform API data to match our component structure
  const categoryListData: CategoryListProps[] = categoriesData.map(
    (category) => ({
      id: category.id,
      title: category.name,
      iconSource: category.media?.fileUrl
        ? { uri: category.media.fileUrl }
        : require('../../../../assets/icons/categories/popular.png'),
      itemsList: category.childCategories || [],
    }),
  );

  return (
    <View className="flex-1">
      <ScreensHeader Heading1="Categories" />
      {/* Categories */}
      {/* <View className="my-6">
        <ScrollView
          showsHorizontalScrollIndicator={false}
          horizontal
          className="px-5"
        >
          <View className="flex-row items-center">
            {categoriesData.map((category) => (
              <View key={category.id} className="mr-5">
                <Pressable
                  className={`${categorySelected === category.id ? 'bg-primary-500' : 'bg-[#F5F5F5]'} p-2.5 rounded-lg self-center`}
                  onPress={() => {
                    setCategorySelected(category.id);
                    router.push(`/store/category/${category.id}?type=parent`);
                  }}
                >
                  <CustomImage
                    source={category.media?.fileUrl
                      ? { uri: category.media.fileUrl }
                      : require('../../../../assets/icons/categories/popular.png')}
                    size="large"
                    onPress={() => {
                      setCategorySelected(category.id);
                      router.push(`/store/category/${category.id}?type=parent`);
                    }}
                  />
                </Pressable>
                <Text
                  className={`mt-1 text-sm font-medium font-noto ${categorySelected === category.id ? 'text-text-600' : 'text-text-400'}`}
                >
                  {capitalizeWords(category.name)}
                </Text>
              </View>
            ))}
          </View>
        </ScrollView>
      </View> */}

      {/* menu */}
      <ScrollView className="px-5" showsVerticalScrollIndicator={false}>
        {categoryListData.map((item) => (
          <CategoryList key={item.id} item={item} />
        ))}
      </ScrollView>
    </View>
  );
};

export default Categories;
