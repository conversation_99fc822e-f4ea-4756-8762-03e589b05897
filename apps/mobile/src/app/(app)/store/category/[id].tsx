import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  Pressable,
  FlatList,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { api } from '@/utils/api';
import { ShopCard } from '@/components/shop-card';
import { useAuth } from '@/lib/auth';
import CustomImage from '@/components/ui/image';
import { Ionicons } from '@expo/vector-icons';
import { capitalizeWords } from '@/utils/text-formatting';
import { IProduct } from '@/lib/_models/product.model';

const CategoryProducts = () => {
  const { id, type = 'parent' } = useLocalSearchParams<{
    id: string;
    type?: 'parent' | 'child';
  }>();
  const router = useRouter();
  const authStatus = useAuth.use.status();
  const [filteredProducts, setFilteredProducts] = useState<IProduct[]>([]);
  const [selectedChildCategory, setSelectedChildCategory] = useState<
    string | null
  >(null);

  // Fetch user data for wishlisted products
  const { data: userData } = api.auth.profile.useQuery(undefined, {
    enabled: authStatus === 'signIn',
  });

  // Fetch products for the category
  const { data, isLoading, isRefetching, isError, refetch } =
    api.getProducts.getProducts.useQuery({
      categoryIds: [id],
      // type: type as 'parent' | 'child',
    });

  useEffect(() => {
    if (!data) return;
    if (!selectedChildCategory) {
      setFilteredProducts(data.allProducts);
      return;
    }
    const filteredProducts = data.allProducts.filter(
      (product) => product.categoryId === selectedChildCategory,
    );
    setFilteredProducts(filteredProducts);
  }, [selectedChildCategory, data?.allProducts]);

  // If a child category is selected, filter products by that child category
  // const filteredProducts = data?.allProducts;

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#2AA952" />
      </View>
    );
  }

  if (isError || !data) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text>Unable to load products.</Text>
      </View>
    );
  }

  const handleChildCategoryPress = (childCategoryId: string) => {
    if (selectedChildCategory === childCategoryId) {
      setSelectedChildCategory(null); // Deselect if already selected
    } else {
      setSelectedChildCategory(childCategoryId);
    }
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={isRefetching} onRefresh={refetch} />
      }
    >
      {/* Display child categories if this is a parent category */}
      {data?.childCategories && data.childCategories.length > 0 && (
        <View className="my-4">
          <Text className="px-5 mb-2 text-lg font-semibold">
            Sub Categories
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="px-5"
          >
            <View className="flex-row items-center">
              {data?.childCategories.map((childCategory) => (
                <Pressable
                  key={childCategory.id}
                  className={`mr-4 p-2 rounded-lg ${
                    selectedChildCategory === childCategory.id
                      ? 'bg-primary-500'
                      : 'bg-[#F5F5F5]'
                  }`}
                  onPress={() => handleChildCategoryPress(childCategory.id)}
                >
                  <Text
                    className={`text-sm font-medium ${
                      selectedChildCategory === childCategory.id
                        ? 'text-white'
                        : 'text-text-600'
                    }`}
                  >
                    {capitalizeWords(childCategory.name)}
                  </Text>
                </Pressable>
              ))}
            </View>
          </ScrollView>
        </View>
      )}

      {/* Display products */}
      <View className="px-5">
        {/* <Text className="mb-4 text-lg font-semibold">
            {selectedChildCategory
              ? data.category?.childCategories?.find(c => c.id === selectedChildCategory)?.name
                ? capitalizeWords(data.category.childCategories.find(c => c.id === selectedChildCategory)!.name)
                : 'Products'
              : 'All Products'}
          </Text> */}

        {filteredProducts && filteredProducts.length > 0 ? (
          filteredProducts.map((product) => (
            <View key={product.id} className="mb-6">
              <ShopCard
                product={product}
                isInitiallyWishlisted={
                  product.userWishlisted?.some(
                    (i) => i.email === userData?.email,
                  ) || false
                }
                productVariant={product.productVariants[0]}
                onPress={() =>
                  router.push(`/store-screens/product-details/${product.id}`)
                }
              />
            </View>
          ))
        ) : (
          <View className="py-10 items-center">
            <Text>No products found in this category.</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

export default CategoryProducts;
