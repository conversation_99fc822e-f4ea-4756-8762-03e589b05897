import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  Dimensions,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Input } from '@/components/ui';
import CustomImage from '@/components/ui/image';
import { ShopCard } from '@/components/shop-card';
import { router, useNavigation } from 'expo-router';
import { DrawerActions } from '@react-navigation/native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { api } from '@/utils/api';
import { useAuth } from '@/lib/auth';

const { height, width } = Dimensions.get('screen');

type NavigationProp = DrawerNavigationProp<any>;

const CategoriesHeading = ({
  title,
  onPress,
}: {
  title: string;
  onPress?: () => void;
}) => (
  <Pressable
    className="mb-4 px-5 flex-row items-center justify-between"
    onPress={onPress}
  >
    <Text className="font-archivo text-xl font-medium text-text-600">
      {title}
    </Text>
    {/* <View className="flex-row items-center">
      <Text className="text-sm font-medium font-noto text-text-600">
        See all
      </Text>
      <CustomImage
        source={require('../../../../assets/icons/ChevronRight.png')}
        size="xs"
      />
    </View> */}
  </Pressable>
);

const Store = () => {
  // TODO: For now we are commenting this to avoid the error of the api
  const [categorySelected, setCategorySelected] = useState<string>();
  const navigation = useNavigation<NavigationProp>();

  const {
    data,
    isLoading: isLoadingProducts,
    isRefetching,
    isError: isErrorProducts,
    refetch,
  } = api.getProducts.getProducts.useQuery({});

  // const { data: filteredData, isLoading: isLoadingFiltered } =
  //   api.getProducts.getProductsByCategoryId.useQuery(
  //     {
  //       id: categorySelected,
  //     },
  //     { enabled: !!categorySelected },
  //   );
  const authStatus = useAuth.use.status();

  const { data: userData } = api.auth.profile.useQuery(undefined, {
    enabled: authStatus === 'signIn',
  });
  // const categoriesData = data?.categories;
  // const filteredProducts = filteredData;
  // console.log('filtered data', filteredData);
  // // Deduplicate parent categories based on their ID
  // const uniqueParentCategories = useMemo(() => {
  //   if (!categoriesData) return [];
  //   return Array.from(
  //     new Map(
  //       categoriesData.map(({ parentCategory }) => [
  //         parentCategory?.id,
  //         parentCategory,
  //       ]),
  //     ).values(),
  //   );
  // }, [categoriesData]);

  // const handleCategorySelect = (categoryId: string) => {
  //   if (categorySelected === categoryId) {
  //     setCategorySelected(undefined); // Clear selection if same category is clicked
  //   } else {
  //     setCategorySelected(categoryId);
  //   }
  // };

  // Early return for loading or error states of products
  if (isLoadingProducts) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (isErrorProducts /*|| !categoriesData */) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text>Unable to load products.</Text>
      </View>
    );
  }

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={isRefetching} onRefresh={refetch} />
      }
    >
      <View className="px-5 pt-3">
        {/* Auto complete products */}
        <Pressable
          onPress={() => {
            console.log('pressed');
            router.navigate('/store/search');
          }}
        >
          <View pointerEvents="none">
            <Input
              variant="tabsSearch"
              placeholder="Search for product..."
              iconSource={require('../../../../assets/icons/Search.png')}
              readOnly
            />
          </View>
        </Pressable>
      </View>
      {/* Categories *}
        <ScrollView showsHorizontalScrollIndicator={false} horizontal>
          <View className="my-6 flex-row items-center ">
            {uniqueParentCategories.slice(0, 5).map(
              (parents) =>
                parents && (
                  <View key={parents.id} className="mr-5 items-center w-[78px]">
                    <Pressable
                      onPress={() => handleCategorySelect(parents.id as string)}
                      className={`p-2.5 rounded-lg ${
                        categorySelected === parents?.id
                          ? 'bg-primary-500'
                          : 'bg-[#F5F5F5]'
                      }`}
                    >
                      <CustomImage
                        source={require('../../../../assets/icons/categories/popular.png')}
                        size="large"
                        onPress={() =>
                          parents && handleCategorySelect(parents.id)
                        }
                      />
                    </Pressable>
                    <Text
                      className={`flex-1 mt-1 text-sm font-medium font-noto text-center ${
                        categorySelected === parents?.id
                          ? 'text-text-600'
                          : 'text-text-400'
                      }`}
                    >
                      {parents?.name}
                    </Text>
                  </View>
                ),
            )}
            {uniqueParentCategories.length > 5 && (
              <Pressable
                onPress={() =>
                  navigation.dispatch(DrawerActions.toggleDrawer())
                }
                className="justify-center"
              >
                <Text className="text-sm font-medium font-noto text-text-400">
                  See all categories
                </Text>
              </Pressable>
            )}
          </View>
        </ScrollView>
      </View> */}

      {/* Display products based on category selection */}
      {/* {!categorySelected &&
        uniqueParentCategories.map((parentCategory) => {
          const allProducts = parentCategory?.childCategories.flatMap(
            (child) => child.products || [],
          );
          if (!allProducts || allProducts.length === 0) {
            return null;
          }
          return ( */}
      {data?.allProducts.map((product) => (
        <View key={product.id}>
          {/* <CategoriesHeading title={parentCategory?.name as string} /> */}
          {/* <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {allProducts?.map((product) => { */}
          {/* const variant = product.productVariants[0]; */}
          {/* return ( */}
          <View key={product.id} className="mb-6">
            <ShopCard
              product={product}
              isInitiallyWishlisted={product.userWishlisted.some(
                (i) => i.email === userData?.email,
              )}
              productVariant={product.productVariants[0]}
              onPress={() =>
                router.push(`/store-screens/product-details/${product.id}`)
              }
            />
          </View>
          {/* );
                })}
              </ScrollView> */}
        </View>
      ))}
      {/* ); */}
      {/* })} */}

      {/* Display filtered products */}
      {/* {categorySelected && (
        <>
          {isLoadingFiltered ? (
            <View className="flex-1 items-center justify-center py-4">
              <ActivityIndicator />
            </View>
          ) : filteredProducts && filteredProducts.length > 0 ? (
            <View>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {filteredProducts.map((product) => (
                  <View
                    key={product.id}
                    style={{ width: width * 0.5 }}
                    className="mb-6"
                  >
                    <ShopCard
                      id={product.id}
                      isInitiallyWishlisted={product.userWishlisted.some(
                        (i) => i.email === userData?.email,
                      )}
                      name={product.name}
                      priceInCents={product.productVariants[0]?.priceInCents}
                      discountInPercentage={
                        product.productVariants[0]?.discountInPercentage
                      }
                      fileUrl={product?.media?.[0]?.media?.fileUrl}
                      avgRating={product.avgRating}
                      totalReviews={product.totalReviews}
                      onPress={() =>
                        router.push('/store-screens/product-details')
                      }
                    />
                  </View>
                ))}
              </ScrollView>
            </View>
          ) : (
            <View className="flex-1 items-center justify-center py-4">
              <Text>No products found in this category</Text>
            </View>
          )}
        </>
      )} */}
    </ScrollView>
  );
};

export default Store;
