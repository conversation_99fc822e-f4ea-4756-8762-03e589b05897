import CustomImage from '@/components/ui/image';
import { Input } from '@/components/ui/input';
import { api } from '@/utils/api';
import React, { useState } from 'react';
import AntDesign from '@expo/vector-icons/AntDesign';
import {
  ActivityIndicator,
  View,
  Text,
  ScrollView,
  Pressable,
} from 'react-native';
import { router } from 'expo-router';

const AutoCompleteComponent = () => {
  const [searchText, setSearchText] = useState('');

  const {
    data: autoCompleteData,
    isLoading: isLoadingAutoComplete,
    isError: isErrorAutoComplete,
  } = api.searchText.getProducts.useQuery(searchText, {
    enabled: !!searchText,
  });

  const goToProduct = (id: string) =>
    router.navigate(`/store-screens/product-details/${id}`);

  const hasResults = autoCompleteData && autoCompleteData.length > 0;

  return (
    <View className="mx-2">
      <View className="px-3 pt-3">
        <Input
          autoFocus={true}
          variant="tabsSearch"
          placeholder="Search for product..."
          iconSource={require('../../../../assets/icons/Search.png')}
          onChangeText={setSearchText}
        />
        <View className="absolute right-6 top-6">
          <Pressable onPress={() => router.back()}>
            <AntDesign name="closecircle" size={24} color="black" />
          </Pressable>
        </View>
      </View>
      {searchText ? (
        <>
          {isLoadingAutoComplete && (
            <View className="mt-2">
              <ActivityIndicator />
            </View>
          )}
          {isErrorAutoComplete && (
            <View className="my-5 justify-center items-center">
              <Text>Searched data not available</Text>
            </View>
          )}
          {!isLoadingAutoComplete && !isErrorAutoComplete && !hasResults && (
            <View className="my-5 justify-center items-center">
              <Text>Product not Available</Text>
            </View>
          )}
          {hasResults && (
            <View className="relative z-10 bg-white p-3">
              <ScrollView
                showsVerticalScrollIndicator={false}
                style={{ maxHeight: 225 }}
              >
                {autoCompleteData.map((item) => (
                  <Pressable key={item.id} onPress={() => goToProduct(item.id)}>
                    <View key={item.id} className="my-2 flex-row items-center">
                      <CustomImage
                        source={item?.media?.[0]?.media?.fileUrl}
                        size="xxl"
                        className="mr-3"
                      />
                      <Text className="text-sm font-normal font-noto text-text-600">
                        {item.name}
                      </Text>
                    </View>
                  </Pressable>
                ))}
              </ScrollView>
            </View>
          )}
        </>
      ) : null}
    </View>
  );
};

const StoreSearchPage = () => {
  return (
    <>
      <AutoCompleteComponent />
    </>
  );
};

export default StoreSearchPage;
