import React, { useState } from 'react';
import { View, Text, ActivityIndicator, Pressable } from '@/components/ui';
import { router } from 'expo-router';
import { useAuth } from '@/lib/auth';
import { showMessage } from 'react-native-flash-message';
import { api } from '@/utils/api';
import { Alert } from 'react-native';
import { format } from 'date-fns';

const DeleteAccount = () => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { signOut } = useAuth();
  const {
    data: deletionStatus,
    refetch,
    isLoading,
  } = api.auth.getDeletionRequestStatus.useQuery();

  const mutation = api.auth.requestAccountDeletion.useMutation({
    onSuccess: (data) => {
      showMessage({
        message: data.message,
        type: 'success',
      });
      void refetch();
    },
    onError: (error) => {
      showMessage({
        message: error.message,
        type: 'danger',
      });
    },
  });

  const revokeMutation = api.auth.revokeDeletionRequest.useMutation({
    onSuccess: (data) => {
      showMessage({
        message: data.message,
        type: 'success',
      });
      void refetch();
    },
    onError: (error) => {
      showMessage({
        message: error.message,
        type: 'danger',
      });
    },
  });

  const handleDeleteAccount = async () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsDeleting(true);
              await mutation.mutate({});
              showMessage({
                message: 'Account deletion requested successfully',
                type: 'success',
              });
              void refetch();
              //   await signOut();
              //   router.replace('/auth');
            } catch (error) {
              console.error('Error deleting account:', error);
              showMessage({
                message:
                  'Failed to request account deletion. Please try again.',
                type: 'danger',
              });
            } finally {
              setIsDeleting(false);
            }
          },
        },
      ],
      { cancelable: true },
    );
  };

  const handleRevoke = () => {
    revokeMutation.mutate();
  };

  if (isLoading) {
    return (
      <View className="flex-1 bg-white p-4">
        <ActivityIndicator size="large" color="#2AA952" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white p-4">
      <View className="rounded-lg bg-white p-6">
        <Text className="mb-6 text-2xl font-bold text-gray-900 font-noto">
          Delete Account
        </Text>

        {deletionStatus?.hasDeletionRequest ? (
          <View className="space-y-6">
            <View className="rounded-md bg-yellow-50 p-4">
              <View className="flex flex-row">
                <View className="flex-shrink-0">
                  {/* Warning icon */}
                  {/* <Svg
                    width={20}
                    height={20}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="h-5 w-5 text-yellow-400"
                  >
                    <Path
                      fillRule="evenodd"
                      d="M8.485 2.495c.873-1.512 3.057-1.512 3.93 0l6.28 10.875c.873 1.512-.218 3.375-1.965 3.375H4.17c-1.747 0-2.838-1.863-1.965-3.375L8.485 2.495zM10 5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                      clipRule="evenodd"
                      fill="#F59E0B"
                    />
                  </Svg> */}
                </View>
                <View className="ml-3">
                  <Text className="text-sm font-medium text-yellow-800 font-noto">
                    Deletion Request Pending
                  </Text>
                  <View className="mt-2">
                    <Text className="text-sm text-yellow-700 font-noto">
                      Your account deletion request was submitted on{' '}
                      {deletionStatus.requestedAt &&
                        format(
                          new Date(deletionStatus.requestedAt),
                          "MMMM d, yyyy 'at' h:mm a",
                        )}
                      . This request is pending admin approval.
                    </Text>
                  </View>
                  <View className="mt-4">
                    <Pressable
                      className="py-2 px-4 rounded-md border border-yellow-300 bg-yellow-50"
                      onPress={handleRevoke}
                      disabled={revokeMutation.isPending}
                    >
                      <Text className="text-yellow-800 text-center font-noto">
                        {revokeMutation.isPending
                          ? 'Revoking...'
                          : 'Revoke Request'}
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </View>
            </View>
          </View>
        ) : (
          <>
            <View className="mb-8 space-y-4">
              <Text className="text-gray-600 font-noto">
                We're sorry to see you go. Before you proceed with deleting your
                account, please note:
              </Text>
              <View className="space-y-2">
                <View className="flex-row">
                  <Text className="text-gray-600 font-noto mr-2">•</Text>
                  <Text className="text-gray-600 font-noto">
                    This action cannot be undone
                  </Text>
                </View>
                <View className="flex-row">
                  <Text className="text-gray-600 font-noto mr-2">•</Text>
                  <Text className="text-gray-600 font-noto">
                    All your personal data will be permanently deleted
                  </Text>
                </View>
                <View className="flex-row">
                  <Text className="text-gray-600 font-noto mr-2">•</Text>
                  <Text className="text-gray-600 font-noto">
                    You will lose access to all your orders and membership
                    history
                  </Text>
                </View>
                <View className="flex-row">
                  <Text className="text-gray-600 font-noto mr-2">•</Text>
                  <Text className="text-gray-600 font-noto">
                    Any active memberships will be cancelled
                  </Text>
                </View>
              </View>
            </View>

            <View className="space-y-6">
              <View className="flex items-center gap-4">
                <Pressable
                  className="bg-red w-full py-3 rounded-md mb-4"
                  onPress={handleDeleteAccount}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <Text className="text-white text-center font-medium font-noto">
                      Request Account Deletion
                    </Text>
                  )}
                </Pressable>
                <Pressable
                  className="w-full py-3 rounded-md border border-gray-300"
                  onPress={() => router.back()}
                >
                  <Text className="text-center font-medium font-noto">
                    Cancel
                  </Text>
                </Pressable>
              </View>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

export default DeleteAccount;
