import React from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  FlatList,
  RefreshControl,
} from 'react-native';
import { api } from '@/utils/api';
import { format } from 'date-fns';
import Ionicons from '@expo/vector-icons/Ionicons';
import { GameMembershipCard } from '@/components/homepage/game-membership';

const GameMemberships: React.FC = () => {
  const { data, isLoading, error, refetch } =
    api.venueSlots.getAllBookedSlots.useQuery();

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-red-500">
          An error occurred check after some time
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 p-4">
      <FlatList
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={refetch} />
        }
        data={data?.activeSubscriptions}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={() => (
          <View className="flex-1 justify-center items-center">
            <Text className="text-gray-500">
              No active game memberships found.
            </Text>
          </View>
        )}
        renderItem={({ item }) => (
          <GameMembershipCard key={item.id} subscription={item} />
        )}
      />
    </View>
  );
};

export default GameMemberships;
