import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { api } from '@/utils/api';
import { FocusAwareStatusBar } from '@/components/ui';
import { Image } from 'expo-image';
import { router, useLocalSearchParams } from 'expo-router';
import { IOrderDetail } from '@/lib/_models/order.model';
import { format } from 'date-fns';
import { showMessage } from 'react-native-flash-message';

const OrderDetails = () => {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();

  console.log('orderId is', orderId);

  const { data: orderDetail, isLoading } =
    api.ordersEcommerce.getOrderById.useQuery(
      { orderId: orderId || '' },
      { enabled: !!orderId },
    );

  const cancelOrderMutation = api.ordersEcommerce.cancelOrder.useMutation();

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#2AA952" />
      </View>
    );
  }

  if (!orderDetail) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="font-noto text-lg text-gray-500">Order not found</Text>
      </View>
    );
  }

  const handleCancelOrder = async () => {
    try {
      Alert.alert(
        'Cancel Order',
        'Are you sure you want to cancel this order?',
        [
          {
            text: 'No',
            style: 'cancel',
          },
          {
            text: 'Yes',
            style: 'destructive',
            onPress: async () => {
              try {
                await cancelOrderMutation.mutateAsync({
                  orderId: orderDetail.id,
                });
                showMessage({
                  message: 'Order cancelled successfully',
                  type: 'success',
                });
                router.push('/profile-screens/orders');
                // You might want to add a success message or navigation here
              } catch (error) {
                console.error('Error cancelling order:', error);
                showMessage({
                  message: 'Error cancelling order',
                  type: 'danger',
                });
              }
            },
          },
        ],
      );
    } catch (error) {
      console.error('Error showing alert:', error);
    }
  };

  return (
    <ScrollView className="flex-1 bg-white">
      <FocusAwareStatusBar />

      {/* Order Info Header */}
      <View className="bg-gray-100 px-4 py-4">
        <View>
          <Text className="font-archivo text-lg mb-4 font-semibold text-primary">
            Order Id:
            <Text className="font-noto font-normal text-secondary">
              {' '}
              #{orderDetail.id}
            </Text>
          </Text>
        </View>
        <View className="flex-row justify-between mb-4">
          <View>
            <Text className="font-archivo text-sm font-semibold text-gray-500">
              Order Placed
            </Text>
            <Text className="font-noto font-medium text-secondary mt-1">
              {orderDetail.orderPlaced
                ? format(new Date(orderDetail.orderPlaced), 'dd MMM yyyy')
                : ''}
            </Text>
          </View>

          <View>
            <Text className="font-archivo text-sm font-semibold text-gray-500">
              Total
            </Text>
            <View className="flex-row items-center mt-1">
              <Text className="font-noto font-medium text-primary">
                ₹{orderDetail.totalInCent / 100}
              </Text>
            </View>
          </View>
        </View>

        <View className="flex-row justify-between">
          <View>
            <Text className="font-archivo text-sm font-semibold text-gray-500">
              Ship To
            </Text>
            <Text className="font-noto font-medium capitalize text-primary mt-1">
              {orderDetail.address.name}
            </Text>
          </View>
          {orderDetail.expectedDelivery && (
            <View>
              <Text className="font-archivo text-sm font-semibold text-gray-500">
                Expected Delivery
              </Text>
              <Text className="font-noto font-medium text-secondary mt-1">
                {orderDetail.expectedDelivery
                  ? format(
                      new Date(orderDetail.expectedDelivery),
                      'dd MMM yyyy',
                    )
                  : ''}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Shipping Information */}
      <View className="px-4 py-4 border-b border-gray-200">
        <Text className="font-noto font-medium text-secondary mb-2">
          Shipping Information
        </Text>
        <View className="mt-2">
          <Text className="font-noto text-sm text-gray-700 mb-1">
            {orderDetail.address.name}
          </Text>
          <Text className="font-noto text-sm text-gray-700 mb-1">
            {orderDetail.address.houseNo}, {orderDetail.address.area},{' '}
            {orderDetail.address.landmark}
          </Text>
          <Text className="font-noto text-sm text-gray-700 mb-1">
            {orderDetail.address.city}, {orderDetail.address.state}{' '}
            {orderDetail.address.pincode}
          </Text>
          <Text className="font-noto text-sm text-gray-700">
            Phone: {orderDetail.address.mobile}
          </Text>
        </View>
      </View>

      {/* Payment Information */}
      <View className="px-4 py-4 border-b border-gray-200">
        <Text className="font-noto font-medium text-secondary mb-2">
          Payment Information
        </Text>
        <View className="mt-2">
          <Text className="font-noto text-sm text-gray-700 mb-1">
            Payment Method: Online
          </Text>
          <Text className="font-noto text-sm text-gray-700">
            Payment Status: {orderDetail.status}
          </Text>
        </View>
      </View>

      {/* Order Actions */}
      <View className="px-4 py-4 border-b border-gray-200">
        <View className="flex-row gap-2">
          {orderDetail.status === 'PAYMENT_SUCCESSFUL' && (
            <Pressable
              className="bg-red px-4 py-2 rounded-md"
              onPress={handleCancelOrder}
            >
              <Text className="text-white font-noto font-medium">
                Cancel Order
              </Text>
            </Pressable>
          )}
          {/* {orderDetail.status === 'PAYMENT_SUCCESSFUL' && (
            <Pressable
              className="bg-primary-500 px-4 py-2 rounded-md"
              onPress={() => {
                orderDetail.
              }}
            >
              <Text className="text-white font-noto font-medium">
                Track Order
              </Text>
            </Pressable>
          )} */}

          {/* {orderDetail.status === 'DELIVERED' && (
            <Pressable
              className="bg-primary-500 px-4 py-2 rounded-md"
              onPress={() => {
                router.push(
                  `/profile-screens/review-ratings?orderId=${orderDetail.id}`,
                );
              }}
            >
              <Text className="text-white font-noto font-medium">
                Review & Ratings
              </Text>
            </Pressable>
          )} */}
        </View>
      </View>

      {/* Line Items */}
      <View className="px-4 py-4">
        <Text className="font-noto font-medium text-secondary mb-4">
          Order Items
        </Text>

        {orderDetail.lineItems.map((item) => (
          <View
            key={item.id}
            className="flex-row mb-6 border-b border-gray-100 pb-4"
          >
            <Image
              source={{
                uri:
                  item.productVariant.product?.media?.[0]?.media.fileUrl ||
                  'https://via.placeholder.com/100',
              }}
              className="w-24 h-24 rounded-md"
            />

            <View className="ml-4 flex-1">
              <Text className="font-noto text-base font-semibold text-secondary mb-1">
                {item.productVariant.product.name}
              </Text>

              <Text className="font-noto text-sm text-gray-500 mb-2">
                {item.productVariant.name} x {item.quantity}
              </Text>

              <Text className="font-noto text-base font-bold mb-3">
                ₹{item.totalInCent / 100}
              </Text>

              <View className="flex-row gap-3">
                {/* {item.productVariant.productVariantInventory &&
                item.productVariant.productVariantInventory.available > 0 ? (
                  <Pressable
                    className="bg-primary-500 px-3 py-2 rounded-md"
                    onPress={() => {
                      // Navigate to product detail to buy again
                      router.push(`/product/${item.productVariant.product.id}`);
                    }}
                  >
                    <Text className="text-white font-noto font-medium text-sm">
                      Buy Again
                    </Text>
                  </Pressable>
                ) : (
                  <View className="bg-transparent border border-red-700 px-3 py-2 rounded-md">
                    <Text className="text-red-700 font-noto font-medium text-sm">
                      Out of stock
                    </Text>
                  </View>
                )} */}

                {/* {orderDetail.status === 'DELIVERED' && (
                  <Pressable
                    className="bg-transparent border border-primary-500 px-3 py-2 rounded-md"
                    onPress={() => {
                      router.push(
                        `/profile-screens/review-ratings?productId=${item.productVariant.product.id}`,
                      );
                    }}
                  >
                    <Text className="text-primary-500 font-noto font-medium text-sm">
                      Review & Ratings
                    </Text>
                  </Pressable>
                )} */}
              </View>
            </View>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

export default OrderDetails;
