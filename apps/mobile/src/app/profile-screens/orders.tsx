import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import { api } from '@/utils/api';
import { FocusAwareStatusBar } from '@/components/ui';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import { IOrderDetail } from '@/lib/_models/order.model';

const Orders = () => {
  const [activeTab, setActiveTab] = useState<
    'inProgress' | 'delivered' | 'cancelled'
  >('inProgress');

  const { data: inProgressOrders, isLoading: isLoadingActive } =
    api.ordersEcommerce.getOrders.useQuery({
      status: 'PAYMENT_SUCCESSFUL',
    });

  const { data: deliveredOrders, isLoading: isLoadingDelivered } =
    api.ordersEcommerce.getOrders.useQuery({
      status: 'DELIVERED',
    });

  const { data: cancelledOrders, isLoading: isLoadingCancelled } =
    api.ordersEcommerce.getOrders.useQuery({
      status: 'CANCELLED',
    });

  const renderOrderItem = (order: IOrderDetail) => {
    return (
      <View key={order.id} className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <View className="flex-row justify-between items-center mb-3">
          <Text className="font-bold text-base font-noto">
            Order #{order.id}
          </Text>
          <Text className="text-primary-500 font-semibold text-sm font-noto">
            {order.expectedDelivery &&
              new Date(order.expectedDelivery).toLocaleDateString()}
          </Text>
        </View>

        {order.lineItems.map((item) => (
          <View
            key={item.id}
            className="flex-row mb-3 border-b border-gray-100 pb-3"
          >
            <Image
              source={{
                uri:
                  item.productVariant.product?.media?.[0]?.media.fileUrl ||
                  'https://via.placeholder.com/100',
              }}
              className="w-20 h-20 rounded-md"
            />
            <View className="ml-3 flex-1">
              <Text className="font-medium text-sm font-noto">
                {item.productVariant.product.name}
              </Text>
              <Text className="text-gray-500 text-xs mt-1 font-noto">
                {item.productVariant.name} x {item.quantity}
              </Text>
              <Text className="font-bold mt-1 font-noto">
                ₹{item.totalInCent / 100}
              </Text>
            </View>
          </View>
        ))}

        <View className="flex-row justify-between items-center mt-2">
          <Text className="font-bold font-noto">
            Total: ₹{order.totalInCent / 100}
          </Text>
          <Pressable
            className="bg-primary-500 px-4 py-2 rounded-md"
            onPress={() => {
              // Navigate to order details
              router.push(`/profile-screens/order-details?orderId=${order.id}`);
            }}
          >
            <Text className="text-white font-medium font-noto">
              View Details
            </Text>
          </Pressable>
        </View>
      </View>
    );
  };

  const renderTabContent = () => {
    if (activeTab === 'inProgress') {
      return isLoadingActive ? (
        <ActivityIndicator size="large" color="#2AA952" className="mt-10" />
      ) : inProgressOrders && inProgressOrders.length > 0 ? (
        inProgressOrders.map(renderOrderItem)
      ) : (
        <Text className="text-center mt-10 text-gray-500 font-noto">
          No active orders found
        </Text>
      );
    } else if (activeTab === 'delivered') {
      return isLoadingDelivered ? (
        <ActivityIndicator size="large" color="#0000ff" className="mt-10" />
      ) : deliveredOrders && deliveredOrders.length > 0 ? (
        deliveredOrders.map(renderOrderItem)
      ) : (
        <Text className="text-center mt-10 text-gray-500 font-noto">
          No delivered orders found
        </Text>
      );
    } else {
      return isLoadingCancelled ? (
        <ActivityIndicator size="large" color="#0000ff" className="mt-10" />
      ) : cancelledOrders && cancelledOrders.length > 0 ? (
        cancelledOrders.map(renderOrderItem)
      ) : (
        <Text className="text-center mt-10 text-gray-500 font-noto">
          No cancelled orders found
        </Text>
      );
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      <FocusAwareStatusBar />
      {/* <View className="flex-row items-center px-4 py-3 bg-white border-b border-gray-200">
        <Pressable onPress={() => router.back()} className="mr-4">
          <Image
            source={require('../../../assets/icons/back.png')}
            className="w-6 h-6"
          />
        </Pressable>
        <Text className="text-lg font-bold font-noto">My Orders</Text>
      </View> */}

      <View className="flex-row bg-white px-4 py-2">
        <Pressable
          className={`flex-1 items-center py-2 ${activeTab === 'inProgress' ? 'border-b-2 border-primary-500' : ''}`}
          onPress={() => setActiveTab('inProgress')}
        >
          <Text
            className={`font-medium font-noto ${activeTab === 'inProgress' ? 'text-primary-500' : 'text-gray-500'}`}
          >
            In Progress
          </Text>
        </Pressable>
        <Pressable
          className={`flex-1 items-center py-2 ${activeTab === 'delivered' ? 'border-b-2 border-primary-500' : ''}`}
          onPress={() => setActiveTab('delivered')}
        >
          <Text
            className={`font-medium font-noto ${activeTab === 'delivered' ? 'text-primary-500' : 'text-gray-500'}`}
          >
            Delivered
          </Text>
        </Pressable>
        <Pressable
          className={`flex-1 items-center py-2 ${activeTab === 'cancelled' ? 'border-b-2 border-primary-500' : ''}`}
          onPress={() => setActiveTab('cancelled')}
        >
          <Text
            className={`font-medium font-noto ${activeTab === 'cancelled' ? 'text-primary-500' : 'text-gray-500'}`}
          >
            Cancelled
          </Text>
        </Pressable>
      </View>

      <ScrollView className="flex-1 px-4 pt-4">{renderTabContent()}</ScrollView>
    </View>
  );
};

export default Orders;
