import {
  Dimensions,
  Pressable,
  StyleSheet,
  Text,
  View,
  ScrollView,
} from 'react-native';
import React, { useState } from 'react';
import { Entypo } from '@expo/vector-icons';
import CustomImage from '@/components/ui/image';
import { Input, Select } from '@/components/ui';
import { AddCartButton } from '@/components/add-cart-button';
import { BuyButton } from '@/components/buy-button';
import { RatingReview } from '@/components/shop-details-page-components/rating-review';
import { Description } from '@/components/shop-details-page-components/description';
import { Pincode } from '@/components/pincode';
import { router, useLocalSearchParams } from 'expo-router';
import { Rating } from '@/components/rating';
import { api } from '@/utils/api';
import { WishlistButton } from '@/components/ui/wishlist';
import { IProduct, IProductVariant } from '@/lib/_models/product.model';
import { LocationDelivery } from '@/components/location-delivery';
import * as Sharing from 'expo-sharing';
import { Env } from '@env';
import { useCartStore } from '@/store/cart.store';
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '@/lib';
import LoadingPage from '@/components/loading-page';
const { height, width } = Dimensions.get('screen');

type FeaturesProps = {
  label: string;
  img: string;
};
const Features: React.FC<FeaturesProps> = ({ label, img }) => {
  return (
    <View className="items-center">
      <CustomImage source={img} size="xl" />
      <Text className="text-xs font-medium font-noto text-text-700">
        {label}
      </Text>
    </View>
  );
};

const ProductDetails = () => {
  const authStatus = useAuth();
  const options = ['Description', 'Rating & Review'];
  const [productData, setProductData] = useState<IProduct | null>(null);
  const [selected, setSelected] = useState(options[0]);
  const { id } = useLocalSearchParams<{ id: string }>();
  const { data: productDataAuthenticated } =
    api.getProducts.getProductByProductId.useQuery(
      {
        id,
      },
      {
        enabled: authStatus?.status === 'signIn',
      },
    );
  const { data: productDataUnauthenticated } =
    api.getProducts.publicGetProductsByProductId.useQuery(
      {
        id,
      },
      {
        enabled: authStatus?.status !== 'signIn',
      },
    );
  const [selectedVariant, setSelectedVariant] =
    useState<IProductVariant | null>(null);
  const [quantity, setQuantity] = useState(1);
  const { cart, addToCart, buyNow } = useCartStore((c) => ({
    cart: c.cart,
    addToCart: c.addToCart,
    buyNow: c.buyNow,
  }));

  // Set first variant as selected when product data loads
  React.useEffect(() => {
    if (
      productDataAuthenticated?.productVariants &&
      productDataAuthenticated.productVariants.length > 0
    ) {
      setSelectedVariant(productDataAuthenticated.productVariants[0]);
    }
    if (productDataAuthenticated) {
      setProductData(productDataAuthenticated);
    }
    if (productDataUnauthenticated) {
      setProductData(productDataUnauthenticated);
    }
    if (
      productDataUnauthenticated?.productVariants &&
      productDataUnauthenticated.productVariants.length > 0
    ) {
      setSelectedVariant(productDataUnauthenticated.productVariants[0]);
    }
  }, [productDataAuthenticated, productDataUnauthenticated]);

  const getPriceDisplay = (variant: IProductVariant) => {
    const price = variant.priceInCents / 100;

    // Calculate discounted price based on whether discount is in percentage or cents
    let discountedPrice = price;
    let discountPercentage = variant.discountInPercentage;

    if (variant.discountInCents) {
      // If discount is in cents, use that directly
      discountedPrice = price - variant.discountInCents / 100;
    } else if (variant.discountInPercentage) {
      // If discount is in percentage, calculate the discounted price
      discountedPrice = price - (price * variant.discountInPercentage) / 100;
    }

    return {
      price,
      discountPercentage,
      discountedPrice,
    };
  };

  const shareProduct = async () => {
    await Sharing.shareAsync(Env.API_URL + '/products/' + productData.slug);
  };

  const handleAddToCart = () => {
    if (authStatus?.status !== 'signIn') {
      showMessage({
        message: 'Please login to add to cart',
        type: 'danger',
      });
      return;
    }
    if (productData && selectedVariant) {
      addToCart(productData, quantity, selectedVariant);
      router.push('/store-screens/cart-stepper');
    }
  };

  const handleBuyNow = () => {
    if (authStatus?.status !== 'signIn') {
      showMessage({
        message: 'Please login to buy now',
        type: 'danger',
      });
      return;
    }
    if (productData && selectedVariant) {
      buyNow(productData, quantity, selectedVariant);
      router.push('/store-screens/cart-stepper');
    }
  };

  if (!productData) {
    return <LoadingPage />;
  }

  return (
    <>
      <ScrollView showsVerticalScrollIndicator={false} className={'flex-1'}>
        {/* product img*/}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ alignItems: 'center' }}
          bounces={false}
        >
          {productData &&
            productData?.media?.map((mediaUrl) => (
              <View
                key={mediaUrl?.media?.fileKey}
                className="items-center justify-center"
                style={{
                  width: width,
                  aspectRatio: 1,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <CustomImage
                  source={
                    mediaUrl?.media?.fileUrl ||
                    'https://via.placeholder.com/100'
                  }
                  style={{
                    width: '100%',
                    aspectRatio: 1,
                    flex: 1,
                    resizeMode: 'contain',
                  }}
                />
              </View>
            ))}
        </ScrollView>

        <View className="flex-1 px-5">
          {/* product details & rating */}
          <View className="w-full flex-row items-center justify-between">
            <View className="px-2.5 py-2 bg-primary-500 rounded-[4px]">
              <Text className="text-[10px] font-medium font-archivo text-white">
                {productData?.brand?.name ?? ''}
              </Text>
            </View>

            <Rating
              rating={parseFloat(
                Number(productData.avgRating ?? '0').toFixed(1),
              )}
              starSize={14}
            />
          </View>

          {/* name, reviews, share, & likes */}
          <View className="mt-2 mb-3">
            <Text className="text-[22px] text-text-600 font-noto font-semibold">
              {productData.name}
            </Text>

            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Text className="mr-1 text-sm font-medium font-noto text-text-700">
                  {productData.totalReviews}
                </Text>
                <Text className="text-sm font-medium font-archivo text-text-300">
                  Review
                </Text>
              </View>

              <View className="mt-1 flex-row items-center gap-4">
                <Pressable onPress={shareProduct}>
                  <CustomImage
                    source={require('../../../../assets/icons/share.png')}
                    size="medium"
                    onPress={shareProduct}
                  />
                </Pressable>
                <WishlistButton
                  id={productData?.id as string}
                  wishlistFor="product"
                  isWishlisted={false}
                />
              </View>
            </View>
          </View>

          {/* price & discounts*/}
          <View className={`mt-1 mb-2 flex-row items-center justify-between`}>
            <View
              className={`flex-row items-center`}
              style={{ maxWidth: '50%' }}
            >
              <Text className="text-base font-archivo font-normal text-text-500">
                Price:{' '}
              </Text>
              {selectedVariant && (
                <>
                  <Text
                    className="mr-2 font-noto text-lg font-semibold text-black"
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    style={{ maxWidth: '50%' }}
                  >
                    ₹
                    {getPriceDisplay(selectedVariant).discountedPrice.toFixed(
                      2,
                    )}
                  </Text>
                  {(selectedVariant.discountInPercentage ||
                    selectedVariant.discountInCents) && (
                    <>
                      <Text
                        className="mr-2 font-noto text-sm font-semibold text-text-200 line-through"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        style={{ maxWidth: '50%' }}
                      >
                        ₹{getPriceDisplay(selectedVariant).price.toFixed(2)}
                      </Text>
                      <Text className="font-archivo text-sm font-semibold text-primary-500">
                        {selectedVariant.discountInPercentage
                          ? `${selectedVariant.discountInPercentage}% off`
                          : selectedVariant.discountInCents
                            ? `₹${(selectedVariant.discountInCents / 100).toFixed(2)} off`
                            : ''}
                      </Text>
                    </>
                  )}
                </>
              )}
            </View>
            <Text className="text-[13px] font-medium font-archivo text-primary-500">
              Inclusive of all taxes
            </Text>
          </View>

          {/* quantity  and weight */}
          <View className="mb-5 self-start gap-4">
            <View className="flex-row items-center mb-3">
              <Text className="text-base font-archivo font-normal text-text-500 mr-2">
                Quantity:
              </Text>
              <View className="flex-row items-center bg-primary-50 px-2 py-1.5 rounded-md">
                <Pressable
                  onPress={() => {
                    if (quantity > 1) {
                      setQuantity(quantity - 1);
                    }
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Entypo name="minus" size={18} color="#2AA952" />
                </Pressable>

                <Text className="font-noto text-sm font-medium text-black mx-3">
                  {quantity}
                </Text>

                <Pressable
                  onPress={() => {
                    const maxStock =
                      selectedVariant?.productVariantInventory?.available ?? 0;
                    console.log('maxStock', maxStock);
                    if (quantity < maxStock) {
                      setQuantity(quantity + 1);
                    } else {
                      showMessage({
                        message: 'No more stock left',
                        type: 'danger',
                      });
                    }
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Entypo name="plus" size={18} color="#2AA952" />
                </Pressable>
              </View>
            </View>
            <View className="flex-row flex-wrap items-center">
              {productData.productVariants.map((variant) => (
                <Pressable
                  key={variant.id}
                  onPress={() => setSelectedVariant(variant)}
                  className={`mb-2.5 mr-5 self-start rounded-[4px] px-3 py-2 ${
                    selectedVariant?.id === variant.id ? 'bg-black' : 'bg-white'
                  }`}
                >
                  <Text
                    className={`font-noto text-xs font-medium ${
                      selectedVariant?.id === variant.id
                        ? 'text-white'
                        : 'text-text-600'
                    }`}
                  >
                    {variant.name}
                  </Text>
                </Pressable>
              ))}
            </View>
          </View>

          {/* current location */}
          <View className="mt-3 mb-3.5">
            <LocationDelivery />
          </View>

          {/* pincode */}
          <View className="mt-3 mb-3.5">
            <Pincode />
          </View>

          {/* features */}
          {/* <View className="mb-5 flex-row items-center justify-between">
            <Features
              label={'Free Shipping'}
              img={require('../../../../assets/icons/free.png')}
            />
            <Features
              label={'Cash on delivery'}
              img={require('../../../../assets/icons/delivery.png')}
            />
            <Features
              label={'10 day replacement'}
              img={require('../../../../assets/icons/replacement.png')}
            />
          </View> */}

          {/* TODO: add offers as website */}
          {/* <View className="mb-3 flex-row items-center justify-between">
            <Text className="text-base font-semibold font-noto text-black">
              Special Offers
            </Text>
            <Text className="text-sm font-semibold font-archivo text-primary-500">
              +9 Offers
            </Text>
          </View>
          <View className="border-b border-text-100 pb-4">
            <View className="px-4 py-2.5 flex-row items-center border border-primary-300 rounded-lg">
              <Text className="flex-1 mr-4 text-sm font-normal font-noto text-text-500">
                Get Flat 10% Cashback on flex it via us on this product suing
                icici bank credit or debit card
              </Text>
              <CustomImage
                source={require('../../../../assets/images/bajaj.png')}
                style={{ width: width * 0.1767, height: height * 0.0289 }}
              />
            </View>
          </View> */}

          {/* Tab switch logic */}
          <>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="gap-10"
            >
              {/* Description Tab */}
              <Pressable
                className={`px-4 py-2 ${selected === options[0] ? 'border-b-[1.5px] border-primary-500' : ''}`}
                onPress={() => setSelected(options[0])}
              >
                <Text
                  className={`text-base font-noto ${selected === options[0] ? 'font-semibold text-text-600' : 'font-normal text-text-500'}`}
                >
                  Description
                </Text>
              </Pressable>

              {/* Rating & Review Tab */}
              {/* <Pressable
                className={`px-4 py-2 ${selected === options[1] ? 'border-b-[1.5px] border-primary-500' : ''}`}
                onPress={() => setSelected(options[1])}
              >
                <Text
                  className={`text-base font-noto ${selected === options[1] ? 'font-semibold text-text-600' : 'font-normal text-text-500'}`}
                >
                  Rating & Review
                </Text>
              </Pressable>*/}
            </ScrollView>

            {/* selected tab rendering conmdition */}
            {selected === options[0] && (
              <Description
                productDetails={
                  productData?.description ?? '<p>nothing found</p>'
                }
              />
            )}
            {/* { selected === options[1] && (
              null
              // <RatingReview />
            )} */}
          </>
        </View>
      </ScrollView>

      {/* buttons */}
      <View className="px-5 pt-3 pb-safe mb-5 flex-row items-center justify-between">
        <AddCartButton showText onPress={handleAddToCart} />
        <BuyButton onPress={handleBuyNow} />
      </View>
    </>
  );
};

export default ProductDetails;

const styles = StyleSheet.create({});
