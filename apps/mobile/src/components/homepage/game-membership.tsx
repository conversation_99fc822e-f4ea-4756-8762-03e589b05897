import { Dimensions } from 'react-native';
import React from 'react';
import { format } from 'date-fns';
import { View, Text } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';

type IGameMembershipCard = {
  subscription: {
    id: string;
    venueGameMemberShip: {
      id: string;
      name: string;
      game: {
        id: string;
        name: string;
        slug: string;
      };
      venue: {
        id: string;
        name: string;
        slug: string;
      };
      daysInPlan: number;
    };
    startDate: Date;
    endDate: Date;
    availableCourts: number;
    bookedCourts: number;
  };
};

export const GameMembershipCard = ({ subscription }: IGameMembershipCard) => {
  return (
    <View
      className="mb-4 p-4 bg-white rounded-lg border-[1.5px] border-[#F5F5F5]"
      style={{ width: Dimensions.get('screen').width * 0.9 }}
    >
      <Text className="text-2xl font-bold mb-2">
        {subscription.venueGameMemberShip.name}
      </Text>
      <View className="flex flex-row items-center gap-2">
        <Ionicons name="location" size={16} color="black" />
        <Text
          className="text-lg font-bold"
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {subscription.venueGameMemberShip.venue.name}
        </Text>
      </View>
      <View className="flex flex-row items-center gap-2">
        <Ionicons name="people" size={16} color="black" />
        <Text className="text-base" numberOfLines={1} ellipsizeMode="tail">
          Game: {subscription.venueGameMemberShip.game.name}
        </Text>
      </View>

      <View className="flex flex-row items-center gap-2">
        <Ionicons name="calendar" size={16} color="black" />
        <Text className="text-base" numberOfLines={1} ellipsizeMode="tail">
          Plan Duration: {subscription.venueGameMemberShip.daysInPlan} days
        </Text>
      </View>
      <View className="flex flex-row justify-between">
        <Text className="text-sm text-gray-600">Validity:</Text>
        <Text className="text-sm text-gray-600">
          {format(subscription.startDate, 'dd MMM yyyy')} -{' '}
          {format(subscription.endDate, 'dd MMM yyyy')}
        </Text>
      </View>
      <View className="flex flex-row justify-between">
        <Text className="text-sm text-gray-600">Remaining Slots:</Text>
        <Text className="text-sm text-gray-600">
          {subscription.availableCourts}
        </Text>
      </View>
      <View className="flex flex-row justify-between">
        <Text className="text-sm text-gray-600">Booked Slots:</Text>
        <Text className="text-sm text-gray-600">
          {subscription.bookedCourts}
        </Text>
      </View>
    </View>
  );
};
