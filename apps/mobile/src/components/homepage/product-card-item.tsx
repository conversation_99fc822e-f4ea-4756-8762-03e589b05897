import * as React from 'react';
import { Image } from 'expo-image';
import { View, Text, TouchableOpacity, Pressable } from 'react-native';
import { ButtonOptimised } from '../ui/button-optimised';
import AntDesign from '@expo/vector-icons/AntDesign';
import { IProduct, IProductVariant } from '@/lib/_models/product.model';
import { router } from 'expo-router';
import { useCartStore } from '@/store/cart.store';
import { useAuth } from '@/lib/auth';
import { showMessage } from 'react-native-flash-message';

/**
 * ActionButtons component that displays the cart and buy buttons
 */
const ActionButtons: React.FC<{
  product: IProduct;
  productVariant: IProductVariant;
}> = ({ product, productVariant }) => {
  const { addToCart, buyNow } = useCartStore((state) => ({
    addToCart: state.addToCart,
    buyNow: state.buyNow,
  }));

  const authStatus = useAuth();

  const handleBuyNow = () => {
    if (authStatus.status !== 'signIn') {
      showMessage({
        message: 'Please sign in to buy now',
        type: 'danger',
      });
      router.push('/auth/sign-in');
      return;
    }
    buyNow(product, 1, productVariant);
  };

  const handleAddToCart = () => {
    if (authStatus.status !== 'signIn') {
      showMessage({
        message: 'Please sign in to add to cart',
        type: 'danger',
      });
      router.push('/auth/sign-in');
      return;
    }
    addToCart(product, 1, productVariant);
  };

  return (
    <View className="flex-row items-center mt-3 gap-4">
      <ButtonOptimised variant="ghost" size="iconLg" onPress={handleAddToCart}>
        <Image
          source={{
            uri: 'https://cdn.builder.io/api/v1/image/assets/TEMP/224b9e78c64a5fc44d72d35fb3b4a23045e6bcef?placeholderIfAbsent=true&apiKey=51e786bed75b4c8bb74d69246e2e847a',
          }}
          className="w-6 h-6"
        />
      </ButtonOptimised>
      <ButtonOptimised
        size="lg"
        className="px-6 py-2 flex-1"
        onPress={handleBuyNow}
      >
        <Text className="text-sm font-medium text-white font-archivo">
          Buy Now
        </Text>
      </ButtonOptimised>
    </View>
  );
};

interface RatingDisplayProps {
  rating: string;
  reviewCount: string;
}

/**
 * RatingDisplay component that shows the product rating and review count
 */
const RatingDisplay: React.FC<RatingDisplayProps> = ({
  rating,
  reviewCount,
}) => {
  return (
    <View className="flex-row items-center bg-[#F4FFF4]">
      <View className="flex-row items-center mr-2">
        <AntDesign name="star" size={16} color="#2AA952" />
        <View>
          <Text className="text-sm text-gray-700">{rating}</Text>
        </View>
      </View>
      <View className="h-4 w-px bg-primary-500 mr-2" />
      <View>
        <Text className="text-sm text-gray-700">{reviewCount}</Text>
      </View>
    </View>
  );
};

interface ProductCardItemProps {
  product: IProduct;
  onPress: () => void;
}

/**
 * ProductCardItem component that displays a single product card
 */
const ProductCardItem: React.FC<ProductCardItemProps> = ({
  product,
  onPress,
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/store-screens/product-details/${product.id}`);
    }
  };

  const productVariant = product.productVariants[0];

  return (
    <Pressable onPress={handlePress}>
      <View className="bg-white rounded-lg shadow-md overflow-hidden flex-row mx-4 w-[320px] border-[1.5px] border-[#F5F5F5]">
        <Image
          source={{ uri: product?.media?.[0]?.media?.fileUrl ||
            'https://via.placeholder.com/100' }}
          className="aspect-square w-1/3 object-cover my-auto"
          resizeMode="contain"
        />
        <View className="p-4 w-2/3">
          <View className="mb-2">
            <View className="mb-1">
              <Text className="text-sm font-medium text-gray-800 font-noto flex-wrap">
                {product.name}
              </Text>
            </View>
            <RatingDisplay
              rating={product.avgRating}
              reviewCount={product.totalReviews.toString()}
            />
          </View>
          <ActionButtons product={product} productVariant={productVariant} />
        </View>
      </View>
    </Pressable>
  );
};

export default ProductCardItem;
