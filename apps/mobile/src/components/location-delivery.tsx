import React, { useState } from 'react';
import { View, Text, Pressable, ActivityIndicator } from 'react-native';
import CustomImage from './ui/image';
import * as Location from 'expo-location';
import { format, addDays } from 'date-fns';
import { showMessage } from 'react-native-flash-message';
import { api } from '../utils/api';

export const LocationDelivery = () => {
  const [loading, setLoading] = useState(false);
  const [deliveryDate, setDeliveryDate] = useState<string | null>(null);
  const [location, setLocation] = useState<string | null>(null);
  const [pincode, setPincode] = useState<string | null>(null);

  const shipRocketCheckAvailabilityMutation =
    api.shipRocket.checkDeliveryDate.useMutation();
  const reverseGeocodeMutation = api.shipRocket.reverseGeocode.useMutation();

  const getLocation = async () => {
    console.log('getLocation', 'getLocation');
    try {
      setLoading(true);
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        showMessage({
          message: 'Permission Denied',
          description: 'Location permission is required to get delivery date',
          type: 'danger',
        });
        return;
      }

      const locationData = await Location.getCurrentPositionAsync({});
      const lat = locationData.coords.latitude;
      const long = locationData.coords.longitude;

      // Use the API to get the pincode from coordinates
      const postcode = await reverseGeocodeMutation.mutateAsync({
        lat,
        long,
      });

      setPincode(postcode);

      // Get address details for display
      const [address] = await Location.reverseGeocodeAsync({
        latitude: lat,
        longitude: long,
      });

      if (address) {
        const formattedAddress = `${address.street || ''} ${address.city || ''} ${address.region || ''} ${postcode || ''}`;
        setLocation(formattedAddress);

        // Check delivery availability using ShipRocket API
        if (postcode) {
          const resp = await shipRocketCheckAvailabilityMutation.mutateAsync({
            deliveryPostcode: parseInt(postcode),
            selectedProductVariant: undefined, // This should be set based on the selected product variant
          });

          if (resp.etd) {
            setDeliveryDate(resp.etd);
          } else {
            showMessage({
              message: 'Delivery Unavailable',
              description: 'Not deliverable to your location',
              type: 'warning',
            });
          }
        }
      }
    } catch (error) {
      showMessage({
        message: 'Error',
        description: 'Failed to get location or delivery information',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View>
      <View className="flex-row items-center justify-between">
        <Text className="text-base font-semibold font-noto text-black">
          Delivery & Services
        </Text>
        <Pressable
          className="flex-row items-center gap-1"
          onPress={getLocation}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#2AA952" />
          ) : (
            <CustomImage
              source={require('../../assets/icons/currentlocation.png')}
              size="small"
            />
          )}
          <Text className="text-sm font-normal font-noto text-text-300">
            Use my location
          </Text>
        </Pressable>
      </View>

      {location && (
        <View className="mt-2">
          <Text className="text-sm font-medium font-noto text-text-700">
            Delivery to: {location}
          </Text>
          {deliveryDate ? (
            <Text className="text-sm font-medium font-noto text-primary-500">
              Expected delivery: {deliveryDate}
            </Text>
          ) : pincode ? (
            <Text className="text-sm font-medium font-noto text-red-400">
              Not deliverable to your location
            </Text>
          ) : null}
        </View>
      )}
    </View>
  );
};
