import { Platform } from 'react-native';
import * as AppleAuthentication from 'expo-apple-authentication';
import React from 'react';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import * as Sentry from '@sentry/react-native';
import { setToken } from '@/utils/session-store';
import { router } from 'expo-router';
import { useAuth } from '@/lib/auth';

const LoginApple = () => {
  const signIn = useAuth.use.signIn();
  const appleSignInMutation = api.auth.appleSignIn.useMutation();

  const signInApple = async () => {
    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      if (!credential.identityToken) {
        // || !credential.email) {
        showMessage({
          message: 'Required information is missing from Apple sign in',
          type: 'danger',
        });
        return;
      }

      appleSignInMutation.mutate(
        {
          identityToken: credential.identityToken,
          email: credential.email || undefined,
          firstName: credential.fullName?.givenName || '',
          lastName: credential.fullName?.familyName || undefined,
        },
        {
          onSuccess: (resp) => {
            signIn({ access: resp.jwtToken, refresh: resp.jwtToken });
            setToken(resp.jwtToken);
            router.dismissTo('/');
          },
          onError: (err) => {
            showMessage({
              message: err.message,
              type: 'danger',
            });
          },
        },
      );
    } catch (e: any) {
      Sentry.captureException(e);
      if (e.code === 'ERR_REQUEST_CANCELED') {
        // User canceled the sign-in flow
        return;
      }
      showMessage({
        message: 'Failed to sign in with Apple',
        type: 'danger',
      });
    }
  };

  return (
    <>
      {Platform.OS === 'ios' && (
        <AppleAuthentication.AppleAuthenticationButton
          style={{
            width: '100%',
            height: 50,
            borderRadius: 5,
            backgroundColor: 'white',
          }}
          buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
          buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.WHITE}
          cornerRadius={5}
          onPress={signInApple}
        />
      )}
    </>
  );
};

export default LoginApple;
