import { api } from '@/utils/api';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { Pressable, Image, Text, Platform } from 'react-native';
import * as Sentry from '@sentry/react-native';
import { setToken, getToken } from '@/utils/session-store';
import { router } from 'expo-router';
import {
  isSuccessResponse,
  isErrorWithCode,
  statusCodes,
  GoogleSigninButton,
} from '@react-native-google-signin/google-signin';
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '@/lib/auth';
import * as Google from 'expo-auth-session/providers/google';
import React, { useEffect, useCallback } from 'react';
import { Env } from '@env';

GoogleSignin.configure({
  offlineAccess: true,
  scopes: ['email', 'profile'],
  webClientId:
    '1044169187752-m1rtqoougl3u6sk1d985q884mvo7045q.apps.googleusercontent.com',
  // '1044169187752-ja5ac264sqm2laf7264u69dfe8ipvd1d.apps.googleusercontent.com',
  iosClientId:
    '1044169187752-6rksfh2ttujmuo9abt4ln74klmgd0cun.apps.googleusercontent.com',
});

const LoginGoogle = () => {
  const signIn = useAuth.use.signIn();
  const googleSignInMutation = api.auth.googleSignIn.useMutation();
  const [request, response, promptAsync] = Google.useAuthRequest({
    webClientId: Env.GOOGLE_WEB_CLIENT_ID,
    androidClientId: Env.GOOGLE_ANDROID_CLIENT_ID,
    iosClientId: Env.GOOGLE_IOS_CLIENT_ID,
  });

  const handleAndroidSignIn = useCallback(async () => {
    if (response?.type === 'success') {
      const userInfo = await getUserInfo(
        response.authentication?.accessToken ?? '',
      );

      googleSignInMutation.mutate(
        {
          idToken: response.authentication?.idToken ?? '',
          email: userInfo.email,
          firstName: userInfo.givenName || '',
          lastName: userInfo.familyName || '',
        },
        {
          onSuccess: (resp) => {
            showMessage({
              message: 'Sign in successful',
              type: 'success',
            });
            signIn({ access: resp.jwtToken, refresh: resp.jwtToken });
            setToken(resp.jwtToken);
            router.dismissTo('/');
          },
          onError: (err) => {
            console.log('error', err);
            showMessage({
              message: err.message,
              type: 'danger',
            });
            router.push('/auth/sign-in');
          },
        },
      );
    } else {
      console.log('No response / error in auth', response);
      // showMessage({
      //   message: 'Sign in failed',
      //   type: 'danger',
      // });
      // router.push('/auth/sign-in');
    }
  }, [response]);

  useEffect(() => {
    console.log('response', response, promptAsync);
    if (Platform.OS === 'android') {
      handleAndroidSignIn();
    }
  }, [response, handleAndroidSignIn]);

  const getUserInfo = async (token: string) => {
    //absent token
    if (!token) return;
    //present token
    try {
      const response = await fetch(
        'https://www.googleapis.com/userinfo/v2/me',
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      const user = await response.json();
      //store user information  in Asyncstorage
      return user;
    } catch (error) {
      return null;
    }
  };

  const signInGoogle = async () => {
    console.log('signInGoogle', Platform.OS);
    if (Platform.OS === 'android') {
      await promptAsync({
        showInRecents: true,
        // prompt: 'select_account', // or 'consent'
      });

      return;
    }

    try {
      await GoogleSignin.hasPlayServices();
      const response = await GoogleSignin.signIn();
      if (isSuccessResponse(response)) {
        const userInfo = await GoogleSignin.getCurrentUser();
        if (!userInfo) {
          showMessage({
            message: 'Failed to get user information',
            type: 'danger',
          });
          return;
        }

        const { user } = userInfo;
        if (!user.email) {
          showMessage({
            message: 'Email is required for sign in',
            type: 'danger',
          });
          return;
        }

        if (!userInfo.idToken) {
          showMessage({
            message: 'Failed to get authentication token',
            type: 'danger',
          });
          return;
        }

        googleSignInMutation.mutate(
          {
            idToken: userInfo.idToken,
            email: user.email,
            firstName: user.givenName || '',
            lastName: user.familyName || undefined,
          },
          {
            onSuccess: (resp) => {
              signIn({ access: resp.jwtToken, refresh: resp.jwtToken });
              setToken(resp.jwtToken);
              router.dismissTo('/');
            },
            onError: (err) => {
              console.log('error', err);
              showMessage({
                message: err.message,
                type: 'danger',
              });
            },
          },
        );
      }
    } catch (error) {
      console.log('error', error);
      Sentry.captureException(error);
      if (isErrorWithCode(error)) {
        switch (error.code) {
          case statusCodes.IN_PROGRESS:
            showMessage({
              message: 'Sign in is already in progress',
              type: 'warning',
            });
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            showMessage({
              message: 'Play services not available or outdated',
              type: 'danger',
            });
            break;
          default:
            showMessage({
              message: 'Failed to sign in with Google',
              type: 'danger',
            });
        }
      } else {
        showMessage({
          message: 'An unexpected error occurred',
          type: 'danger',
        });
      }
    }
  };

  return (
    // <GoogleSigninButton
    //   onPress={signInGoogle}
    //   style={{
    //     width: '100%',
    //     height: 50,
    //     borderRadius: 10,
    //   }}
    // />
    <Pressable
      onPress={signInGoogle}
      className="flex-row items-center justify-center h-16 bg-white rounded-md"
    >
      <Image
        source={require('../../assets/icons/google-login.png')}
        className="mr-2"
        style={{ width: 20, height: 20 }}
      />
      <Text className="text-black text-xl font-semibold">
        Sign in with Google
      </Text>
    </Pressable>
  );
};

export default LoginGoogle;
