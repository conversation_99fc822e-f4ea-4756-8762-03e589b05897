import { View, Text, ScrollView, Dimensions, Alert } from 'react-native';
import React, { useState, useMemo, useCallback, useRef } from 'react';
import { OrderAmountPay } from '@/components/order-amount-pay';
import { OrderSummary } from '@/components/order-summary';
import CustomImage from '@/components/ui/image';
import { showMessage } from 'react-native-flash-message';
import { Button, Input, Modal, Radio, useModal } from '@/components/ui';
// import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import {
  Address,
  AddressComponent,
  AddressForm,
} from '@/components/address-component';
import { api } from '@/utils/api';
import { useCartStore } from '@/store/cart.store';

const CartAddress = ({ onPress }: { onPress: () => void }) => {
  const { cart, setAddress } = useCartStore((state) => ({
    cart: state.cart,
    setAddress: state.setAddress,
  }));
  const { data, isLoading, refetch } = api.profile.getAllAddresses.useQuery();
  const createAddressMutation = api.profile.createAddress.useMutation();
  const updateAddressMutation = api.profile.updateAddress.useMutation();
  const deleteAddressMutation = api.profile.deleteAddress.useMutation();
  const [selectedAddressIndex, setSelectedAddressIndex] = useState<
    number | null
  >(null);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);

  const { ref: bottomSheetRef, present, dismiss } = useModal();
  const snapPoints = useMemo(() => ['90%'], []);

  const handleAddAddress = useCallback(() => {
    const newAddress: Address = {
      id: null,
      name: '',
      mobile: '',
      city: '',
      area: '',
      countryId: '',
      stateId: '',
      houseNo: '',
      landmark: '',
      pincode: '',
      addressType: '',
    };
    setSelectedAddress(newAddress);
    present();
  }, []);

  const handleSelectAddress = useCallback((index: number) => {
    setSelectedAddressIndex(index);
  }, []);

  const handleEditAddress = useCallback((address: Address) => {
    present();
    setSelectedAddress(address);
    // bottomSheetRef.current?.expand();
  }, []);

  const handleDeleteAddress = useCallback((id: string) => {
    Alert.alert(
      'Delete Address',
      'Are you sure you want to delete this address?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => {
            deleteAddressMutation.mutate(
              { addressId: id },
              {
                onSuccess: () => {
                  // Optionally, you can refetch the addresses or update the local state
                  console.log('Address deleted successfully');
                },
                onError: (error) => {
                  console.error('Failed to delete address:', error);
                },
              },
            );
          },
          style: 'destructive',
        },
      ],
    );
  }, []);

  const handleSaveAddress = useCallback(
    (updatedAddress: Address) => {
      if (updatedAddress.id) {
        // Update existing address
        updateAddressMutation.mutate(
          {
            addressId: updatedAddress.id,
            data: {
              name: updatedAddress.name,
              mobile: updatedAddress.mobile,
              landmark: updatedAddress.landmark,
              pincode: updatedAddress.pincode,
              addressType: updatedAddress.addressType as
                | 'home'
                | 'office'
                | 'friends/family'
                | 'others',
              houseNo: updatedAddress.houseNo,
              area: updatedAddress.area,
              city: updatedAddress.city,
              stateId: updatedAddress.stateId,
              countryId: updatedAddress.countryId,
            },
          },
          {
            onSuccess: (response) => {
              if (response.error) {
                showMessage({
                  message: response.error,
                  type: 'danger',
                });
              } else {
                refetch();
                dismiss();
                setSelectedAddress(null);
                showMessage({
                  message: 'Address updated successfully',
                  type: 'success',
                });
              }
            },
            onError: (error) => {
              console.error('Failed to update address:', error);
              showMessage({
                message: 'Failed to update address',
                type: 'danger',
              });
            },
          },
        );
      } else {
        // Create new address
        createAddressMutation.mutate(
          {
            name: updatedAddress.name,
            mobile: updatedAddress.mobile,
            landmark: updatedAddress.landmark,
            pincode: updatedAddress.pincode,
            addressType: updatedAddress.addressType as
              | 'home'
              | 'office'
              | 'friends/family'
              | 'others',
            houseNo: updatedAddress.houseNo,
            area: updatedAddress.area,
            city: updatedAddress.city,
            stateId: updatedAddress.stateId,
            countryId: updatedAddress.countryId,
          },
          {
            onSuccess: (response) => {
              if (response.error) {
                showMessage({
                  message: response.error,
                  type: 'danger',
                });
              } else {
                refetch();
                dismiss();
                setSelectedAddress(null);
                showMessage({
                  message: 'Address created successfully',
                  type: 'success',
                });
              }
            },
            onError: (error) => {
              console.error('Failed to create address:', error);
              showMessage({
                message: 'Failed to create address',
                type: 'danger',
              });
            },
          },
        );
      }
    },
    [createAddressMutation, updateAddressMutation, dismiss],
  );

  return (
    <View className="flex-1 justify-between px-5 w-full">
      <ScrollView className={'flex-1'} showsVerticalScrollIndicator={false}>
        <View className="mt-4">
          {data &&
            data.addresses.map((address) => (
              <AddressComponent
                key={address.id}
                address={address}
                onSelect={() => setAddress(address)}
                isSelected={address.id === cart.address?.id}
                onEdit={() => handleEditAddress(address)}
                onDelete={() => address.id && handleDeleteAddress(address.id)}
              />
            ))}
        </View>

        <Button
          variant="outline"
          className="px-6 py-3 mb-5"
          label="+ Add Address"
          onPress={handleAddAddress}
        />

        {/* Coupon offer */}
        {/* <View className="px-4 py-3 border border-primary-300 rounded-lg flex-row items-center justify-between">
          <CustomImage
            source={require('../../../assets/images/discount1.png')}
            size="xxl"
            style={{ flexShrink: 0 }}
          />
          <View className={`ml-1.5 mr-4 flex-row items-center`}>
            <View className={`flex-row items-center flex-1`}>
              <Text
                numberOfLines={2}
                className="font-noto text-sm text-text-400"
              >
                <Text>Get Flat </Text>
                <Text className="font-bold">11% Cash back </Text>
                <Text>on flex it via us on this product</Text>
              </Text>
            </View>
          </View>

          <CustomImage
            source={require('../../../assets/icons/cross.png')}
            size="medium"
            style={{ flexShrink: 0 }}
          />
        </View> */}
      </ScrollView>
      <View>
        {/* Order Summary */}
        <OrderSummary />

        {/* Order Amount and Payment */}
        <OrderAmountPay ButtonLabel={'Continue'} onPress={onPress} />
      </View>

      <Modal ref={bottomSheetRef} snapPoints={snapPoints} enablePanDownToClose>
        {selectedAddress && (
          <AddressForm
            address={selectedAddress}
            onSave={handleSaveAddress}
            onCancel={() => {
              bottomSheetRef.current?.close();
              setSelectedAddress(null);
            }}
          />
        )}
      </Modal>
    </View>
  );
};

export default CartAddress;
