"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import ReCAPTCHA from "react-google-recaptcha";
import { api } from "~/trpc/react";
import { toast } from "~/_components/ui/use-toast";
import { Button } from "~/_components/ui/button";
import Label from "~/ui/form/label";
import { useRouter } from "next/navigation";
import { env } from "~/env";
import { format } from "date-fns";

const formSchema = z.object({
  recaptchaToken: z
    .string()
    .min(1, "Please complete the reCAPTCHA verification"),
});

type FormValues = z.infer<typeof formSchema>;

export default function DeleteAccount() {
  const router = useRouter();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      recaptchaToken: "",
    },
  });

  const { data: deletionStatus, refetch } =
    api.auth.getDeletionRequestStatus.useQuery();

  const mutation = api.auth.requestAccountDeletion.useMutation({
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message,
      });
      void refetch();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const revokeMutation = api.auth.revokeDeletionRequest.useMutation({
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message,
      });
      void refetch();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: FormValues) => {
    mutation.mutate(values);
  };

  const handleRevoke = () => {
    revokeMutation.mutate();
  };

  return (
    <div className="mx-auto max-w-3xl px-4 py-8">
      <div className="rounded-lg bg-white p-6 shadow-md">
        <h1 className="mb-6 text-2xl font-bold text-gray-900">
          Delete Account
        </h1>

        {deletionStatus?.hasDeletionRequest ? (
          <div className="space-y-6">
            <div className="rounded-md bg-yellow-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-yellow-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.485 2.495c.873-1.512 3.057-1.512 3.93 0l6.28 10.875c.873 1.512-.218 3.375-1.965 3.375H4.17c-1.747 0-2.838-1.863-1.965-3.375L8.485 2.495zM10 5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Deletion Request Pending
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      Your account deletion request was submitted on{" "}
                      {deletionStatus.requestedAt &&
                        format(
                          new Date(deletionStatus.requestedAt),
                          "MMMM d, yyyy 'at' h:mm a",
                        )}
                      . This request is pending admin approval.
                    </p>
                  </div>
                  <div className="mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleRevoke}
                      disabled={revokeMutation.isPending}
                      className="text-yellow-800 hover:bg-yellow-100"
                    >
                      {revokeMutation.isPending
                        ? "Revoking..."
                        : "Revoke Request"}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="mb-8 space-y-4 text-gray-600">
              <p>
                We're sorry to see you go. Before you proceed with deleting your
                account, please note:
              </p>
              <ul className="list-inside list-disc space-y-2">
                <li>This action cannot be undone</li>
                <li>All your personal data will be permanently deleted</li>
                <li>
                  You will lose access to all your orders and membership history
                </li>
                <li>Any active memberships will be cancelled</li>
              </ul>
            </div>

            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div>
                <Label>Verify you are human</Label>
                <ReCAPTCHA
                  sitekey={env.NEXT_PUBLIC_SITE_KEY}
                  onChange={(token) =>
                    form.setValue("recaptchaToken", token ?? "")
                  }
                />
                {form.formState.errors.recaptchaToken && (
                  <p className="mt-1 text-sm text-red-600">
                    {form.formState.errors.recaptchaToken.message}
                  </p>
                )}
              </div>

              <div className="flex items-center gap-4">
                <Button
                  type="submit"
                  variant="destructive"
                  disabled={mutation.isPending}
                  className="w-full"
                >
                  {mutation.isPending
                    ? "Processing..."
                    : "Request Account Deletion"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}
