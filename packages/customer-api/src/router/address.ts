import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";

export const addressFormSchema = z.object({
    name: z.string().min(1, "Name is required"),
    mobile: z.string().min(1, "Mobile number is required"),
    landmark: z.string(),
    pincode: z.string().min(1, "Pincode is required"),
    addressType: z.enum(["home", "office", "friends/family", "others"]),
    houseNo: z.string().min(1, "House number is required"),
    area: z.string().min(1, "Area is required"),
    city: z.string().min(1, "City is required"),
    stateId: z.string().min(1, "State ID is required"),
    countryId: z.string().min(1, "Country ID is required"),
});

export const addressRouter = createTRPCRouter({
    getAllCountries: protectedProcedure.query(async ({ ctx }) => {
        const countries = await ctx.db.country.findMany({
            select: {
                id: true,
                name: true,
                states: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            },
            where: {
                active: true,
                deletedAt: null
            }
        });

        return { countries };
    }),

    getAllAddresses: protectedProcedure.query(async ({ ctx }) => {
        const addresses = await ctx.db.address.findMany({
            select: {
                id: true,
                name: true,
                mobile: true,
                countryId: true,
                country: {
                    select: {
                        id: true,
                        name: true,
                    }
                },
                houseNo: true,
                area: true,
                city: true,
                landmark: true,
                pincode: true,
                stateId: true,
                state: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                addressType: true,
            },
            where: {
                userId: ctx.user.id,
                deletedAt: null,
            },
        });

        return { addresses };
    }),

    createAddress: protectedProcedure
        .input(addressFormSchema)
        .mutation(async ({ input, ctx }) => {
            try {
                const creatingAddress = await ctx.db.address.create({
                    data: {
                        name: input.name,
                        mobile: input.mobile,
                        landmark: input.landmark,
                        pincode: input.pincode,
                        addressType: input.addressType,
                        city: input.city,
                        countryId: input.countryId,
                        area: input.area,
                        stateId: input.stateId,
                        houseNo: input.houseNo,
                        userId: ctx.user.id,
                    },
                });

                if (!creatingAddress) {
                    throw new TRPCError({
                        message: "Error in creating the address.",
                        code: "INTERNAL_SERVER_ERROR"
                    });
                }

                return {
                    message: "Address created successfully.",
                };
            } catch (err) {
                throw new TRPCError({
                    message: "Error occurred in creating the address.",
                    code: "INTERNAL_SERVER_ERROR"
                });
            }
        }),

    updateAddress: protectedProcedure
        .input(z.object({
            addressId: z.string(),
            data: addressFormSchema
        }))
        .mutation(async ({ ctx, input }) => {
            try {
                const updatingAddress = await ctx.db.address.update({
                    where: {
                        id: input.addressId,
                        userId: ctx.user.id, // Ensure user can only update their own addresses
                    },
                    data: {
                        name: input.data.name,
                        mobile: input.data.mobile,
                        landmark: input.data.landmark,
                        pincode: input.data.pincode,
                        addressType: input.data.addressType,
                        city: input.data.city,
                        countryId: input.data.countryId,
                        area: input.data.area,
                        stateId: input.data.stateId,
                        houseNo: input.data.houseNo,
                    },
                });

                if (!updatingAddress) {
                    throw new TRPCError({
                        message: "Error in updating the address.",
                        code: "INTERNAL_SERVER_ERROR"
                    });
                }

                return {
                    message: "Address updated successfully.",
                };
            } catch (err) {
                throw new TRPCError({
                    message: "Error occurred in updating the address.",
                    code: "INTERNAL_SERVER_ERROR"
                });
            }
        }),

    deleteAddress: protectedProcedure
        .input(z.object({ addressId: z.string() }))
        .mutation(async ({ input, ctx }) => {
            try {
                const deletedAddress = await ctx.db.address.update({
                    where: {
                        id: input.addressId,
                        userId: ctx.user.id, // Ensure user can only delete their own addresses
                    },
                    data: {
                        deletedAt: new Date(),
                    },
                });

                if (!deletedAddress) {
                    throw new TRPCError({
                        message: "Error in deleting the address.",
                        code: "INTERNAL_SERVER_ERROR"
                    });
                }

                return {
                    message: "Address deleted successfully."
                };
            } catch (err) {
                throw new TRPCError({
                    message: "Error occurred in deleting the address.",
                    code: "INTERNAL_SERVER_ERROR"
                });
            }
        })
}); 