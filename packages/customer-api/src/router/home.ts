import { GymMembershipBookingStatus, VenueBookingStatus, VenueMembershipBookingStatus } from "@repo/database";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";
import { db } from "@repo/customer-auth/src/db";

export const homeRouter = createTRPCRouter({
    getHomeData: publicProcedure.query(async ({ ctx }) => {
      const homeBanners = await ctx.db.homeBanner.findMany({
        select: {
          id: true,
          media: {
              select: {
                  id: true,
                  fileUrl: true,
              },
          },
          url: true,
        },
        where: {    
          active: true,
          deletedAt: null,
          media: {
            fileUrl: {
              not: null,
            },
          },
        },
        orderBy: {
          order: 'asc',
        },
      });

      const bestsellerProducts = await ctx.db.product.findMany({
          select: {
            id: true,
            veg: true,
            name: true,
            description: true,
            avgRating: true,
            totalReviews: true,
            slug: true,
            categoryId: true,
            media: {
              select: {
                media: {
                  select: {
                    fileKey: true,
                    fileUrl: true,
                  },
                },
              },
              orderBy: {
                order: "asc",
              },
            },
            review: {
              select: {
                id: true,
                rating: true,
                review: true,
                createdAt: true,
                approved: true,
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
            productVariants: {
              select: {
                id: true,
                name: true,
                priceInCents: true,
                discountInCents: true,
                discountInPercentage: true,
                discountEndDate: true,
                productVariantInventory: {
                  select: {
                    id: true,
                    available: true,
                    productVariantId: true,
                  },
                },
              },
              where: {
                deletedAt: null,
              },
            },
          },
          where: {
            bestseller: true,
            active: true,
            deletedAt: null,
          },
      });

      return {
        homeBanners,
        bestsellerProducts: bestsellerProducts.filter((product) => product.productVariants.length > 0 && product.media.length > 0).map((product) => ({
          ...product,
          // Sending empty array as no user auth available in public procedure
          userWishlisted: [],
          avgRating: new String(product.avgRating)
        })),
      };
    }),
    protectedHomePage: protectedProcedure.query(async ({ ctx }) => {
      const activeGymMemberships = await ctx.db.gymMembershipBooking.findMany({
        select: {
            id: true,
            startDate: true,
            endDate: true,
            amountInCents: true,
            status: true,
            gymMembership: {
                select: {
                    id: true,
                    name: true,
                    venue: {
                        select: {
                            id: true,
                            name: true,
                            streetAddress: true,
                            state: {
                                select: {
                                    id: true,
                                    name: true,
                                },
                            },
                            latitude: true,
                            longitude: true,
                        },
                    },
                },
            },
        },
        where: {
            userId: ctx.user.id,
            status: GymMembershipBookingStatus.PAYMENT_COMPLETED,
        },
    });

    const upcomingSlotBookings = await db.venueBooking.findMany({
      select: {
        id: true,
        status: true,
        totalAmount: true,
        subTotalInCents:true,
        venue: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        gameSlots: {
          select: {
            id: true,
            gameId: true,
            game: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            slotPrice: true,
            bookedCourts: true,
            startTime: true,
            endTime: true,
          },
        },
      },
      where: {
        gameSlots: {
          some: {
            startTime: {
              gte: new Date(),
            },
          },
        },
        userId: ctx.user.id,
        status: {
          in: [
            VenueBookingStatus.PAYMENT_COMPLETED,
            VenueBookingStatus.PAYMENT_MEMBERSHIP,
          ],
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const activeSlotSubscriptions = await db.venueMembershipBooking.findMany({
      select: {
        id: true,
        venueGameMemberShip: {
          select: {
            id: true,
            name: true,
            daysInPlan: true,
            venue: {
              select: {
                id: true,
                slug: true,
                name: true,
              }
            },
            game: {
              select: {
                id: true,
                slug: true,
                name: true,
              }
            }
          }
        },
        startDate: true,
        endDate: true,
        bookedCourts: true,
        availableCourts: true,
      },
      where: {
        userId: ctx.user.id,
        // startDate: {
        //   lte: new Date()
        // },
        endDate: {
          gte: new Date()
        },
        availableCourts: {
          gt: 0
        },
        status: {
          in: [VenueMembershipBookingStatus.PAYMENT_COMPLETED, VenueMembershipBookingStatus.PAYMENT_OFFLINE]
        },
      },
      orderBy: [{
        startDate: "asc"
      },{
        createdAt: "desc",
      }],
    });

    return {
      activeSlotSubscriptions,
      upcomingSlotBookings,
      activeGymMemberships
    };
  }),
}); 